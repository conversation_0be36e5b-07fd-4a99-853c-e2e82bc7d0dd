#!/bin/bash

# MTBRMG ERP - Performance Optimizer for macOS
# Analyzes and optimizes development environment performance

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

echo -e "${CYAN}🚀 MTBRMG ERP Performance Optimizer${NC}"
echo "=============================================="

# Performance analysis
echo -e "\n${BLUE}📊 System Performance Analysis${NC}"
echo "=============================================="

# Check system resources
echo -e "${YELLOW}💻 System Resources:${NC}"
echo -e "  CPU Cores: $(sysctl -n hw.ncpu)"
echo -e "  Memory: $(echo "$(sysctl -n hw.memsize) / 1024 / 1024 / 1024" | bc)GB"
echo -e "  Architecture: $(uname -m)"

# Check current load
echo -e "\n${YELLOW}⚡ Current System Load:${NC}"
uptime

# Check memory usage
echo -e "\n${YELLOW}🧠 Memory Usage:${NC}"
vm_stat | head -5

# Check disk I/O
echo -e "\n${YELLOW}💾 Disk Performance:${NC}"
df -h . | tail -1

echo -e "\n${BLUE}🔍 Development Environment Analysis${NC}"
echo "=============================================="

# Check Node.js version and performance
echo -e "${YELLOW}📦 Node.js Environment:${NC}"
if command -v node &> /dev/null; then
    echo -e "  Version: $(node --version)"
    echo -e "  NPM: $(npm --version)"
    echo -e "  PNPM: $(pnpm --version 2>/dev/null || echo 'Not installed')"
else
    echo -e "  ${RED}❌ Node.js not found${NC}"
fi

# Check Python environment
echo -e "\n${YELLOW}🐍 Python Environment:${NC}"
if command -v python3 &> /dev/null; then
    echo -e "  Version: $(python3 --version)"
    echo -e "  Pip: $(pip3 --version | cut -d' ' -f2)"
else
    echo -e "  ${RED}❌ Python3 not found${NC}"
fi

# Check database performance
echo -e "\n${YELLOW}🗄️ Database Performance:${NC}"
if brew services list | grep postgresql | grep started > /dev/null; then
    echo -e "  PostgreSQL: ${GREEN}✅ Running${NC}"
    # Test database connection speed
    start_time=$(date +%s%N)
    psql -h localhost -U muhammadyoussef -d mtbrmg_erp -c "SELECT 1;" > /dev/null 2>&1
    end_time=$(date +%s%N)
    duration=$(echo "scale=3; ($end_time - $start_time) / 1000000" | bc)
    echo -e "  Connection Speed: ${duration}ms"
else
    echo -e "  PostgreSQL: ${RED}❌ Not running${NC}"
fi

# Check Redis performance
echo -e "\n${YELLOW}⚡ Redis Performance:${NC}"
if brew services list | grep redis | grep started > /dev/null; then
    echo -e "  Redis: ${GREEN}✅ Running${NC}"
    # Test Redis connection speed
    start_time=$(date +%s%N)
    redis-cli ping > /dev/null 2>&1
    end_time=$(date +%s%N)
    duration=$(echo "scale=3; ($end_time - $start_time) / 1000000" | bc)
    echo -e "  Connection Speed: ${duration}ms"
else
    echo -e "  Redis: ${RED}❌ Not running${NC}"
fi

echo -e "\n${BLUE}🚀 Performance Optimization Solutions${NC}"
echo "=============================================="

echo -e "${PURPLE}1. 🏎️ ULTRA-FAST DEVELOPMENT WITH VITE${NC}"
echo "   Replace Next.js dev server with Vite for 10x faster builds"
echo "   Command: ./scripts/setup-vite-dev.sh"

echo -e "\n${PURPLE}2. 🐳 OPTIMIZED DOCKER SETUP${NC}"
echo "   Use Docker with performance optimizations"
echo "   Command: ./scripts/setup-fast-docker.sh"

echo -e "\n${PURPLE}3. 🔥 NATIVE macOS OPTIMIZATION${NC}"
echo "   Optimize macOS settings for development"
echo "   Command: ./scripts/optimize-macos.sh"

echo -e "\n${PURPLE}4. ⚡ TURBO DEVELOPMENT MODE${NC}"
echo "   Use Turbo for monorepo optimization"
echo "   Command: ./scripts/setup-turbo-dev.sh"

echo -e "\n${PURPLE}5. 🚀 PRODUCTION-LIKE LOCAL SETUP${NC}"
echo "   Use production builds locally with hot reload"
echo "   Command: ./scripts/setup-prod-local.sh"

echo -e "\n${BLUE}💡 Quick Performance Fixes${NC}"
echo "=============================================="

echo -e "${YELLOW}Immediate Actions:${NC}"
echo "1. Increase Node.js memory: export NODE_OPTIONS='--max-old-space-size=8192'"
echo "2. Use SWC instead of Babel: Already configured ✅"
echo "3. Enable filesystem caching: Already configured ✅"
echo "4. Optimize database connections: Use connection pooling"
echo "5. Use faster package manager: PNPM (already using) ✅"

echo -e "\n${YELLOW}System Optimizations:${NC}"
echo "1. Disable Spotlight indexing for node_modules"
echo "2. Exclude development folders from Time Machine"
echo "3. Use faster DNS servers (*******, *******)"
echo "4. Increase file descriptor limits"

echo -e "\n${BLUE}🎯 Recommended Solution for Your Case${NC}"
echo "=============================================="

echo -e "${GREEN}🏆 BEST OPTION: Vite + Express Setup${NC}"
echo "This will give you:"
echo "  ⚡ 10x faster hot reload"
echo "  🚀 Instant server startup"
echo "  💾 Lower memory usage"
echo "  🔥 Better development experience"

echo -e "\n${CYAN}Would you like to implement the ultra-fast setup? (y/n)${NC}"

# MTBRMG ERP System: Docker to Local Development Transition Analysis

## Executive Summary

This document provides a comprehensive analysis of the current Docker-based MTBRMG ERP system and outlines the transition plan to local macOS development environment.

## Current Docker Architecture Analysis

### 1. Service Architecture
The system currently runs 5 Docker services:

#### Core Services:
- **PostgreSQL Database** (`postgres:15-alpine`)
  - Container: `mtbrmg_postgres`
  - Port: `5432:5432`
  - Database: `mtbrmg_erp`
  - Credentials: `postgres/postgres`

- **Redis Cache/Queue** (`redis:7-alpine`)
  - Container: `mtbrmg_redis`
  - Port: `6379:6379`
  - Used for caching and Celery task queue

#### Application Services:
- **Django Backend** (Custom build)
  - Container: `mtbrmg_backend`
  - Port: `8000:8000`
  - Framework: Django 4.2.9
  - API endpoints and admin interface

- **Next.js Frontend** (Custom build)
  - Container: `mtbrmg_frontend`
  - Port: `3001:3001`
  - Framework: Next.js 15.2.4
  - Unified founder dashboard architecture

#### Background Services:
- **Celery Beat** (Scheduled tasks)
  - Container: `mtbrmg_celery_beat`
  - No exposed ports
  - Handles scheduled background tasks

### 2. Docker Files Identified for Removal

#### Primary Docker Configuration:
- `docker-compose.yml` (root level)
- `docker/docker-compose.yml`
- `docker-compose.prod.yml`
- `.env.production`

#### Docker Build Files:
- `docker/Dockerfile.backend`
- `docker/Dockerfile.frontend`
- `apps/backend/Dockerfile.prod`
- `apps/frontend/Dockerfile.prod`

#### Docker Ignore Files:
- `apps/frontend/.dockerignore`
- `apps/backend/.dockerignore` (if exists)

#### Docker Scripts:
- `docker/start-backend.sh`
- `docker/init-db.sql`
- `scripts/monitor.sh` (Docker-dependent)
- `scripts/deploy.sh` (Docker-dependent)

#### Docker-specific Environment Files:
- `apps/backend/.env.docker`
- `apps/frontend/.env.docker` (if exists)

#### Docker Utility Scripts:
- `docker_console_fix.py`

### 3. Environment Variables Analysis

#### Current Docker Environment (apps/backend/.env.docker):
```env
DEBUG=True
SECRET_KEY=django-insecure-docker-development-key-change-in-production

# Database Configuration
DB_NAME=mtbrmg_erp
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=postgres  # ← Container hostname
DB_PORT=5432
USE_SQLITE=False

# Redis Configuration
REDIS_URL=redis://redis:6379/0  # ← Container hostname

# CORS Configuration
ALLOWED_HOSTS=localhost,127.0.0.1,backend,frontend
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://frontend:3001
```

#### Required Local Environment Changes:
- `DB_HOST`: `postgres` → `localhost`
- `REDIS_URL`: `redis://redis:6379/0` → `redis://localhost:6379/0`
- Remove container hostnames from CORS settings
- Update API URLs in frontend configuration

### 4. Service Dependencies

#### Backend Dependencies:
- PostgreSQL database connection
- Redis for caching and Celery
- Static file serving
- Media file handling

#### Frontend Dependencies:
- Backend API at `http://localhost:8000`
- Next.js API rewrites configuration
- Static asset optimization

#### Inter-service Communication:
- Frontend → Backend: HTTP API calls
- Backend → PostgreSQL: Database queries
- Backend → Redis: Cache operations and Celery tasks
- Celery → Redis: Task queue management

### 5. Port Mapping Analysis

#### Current Docker Ports:
- `5432` - PostgreSQL (standard port)
- `6379` - Redis (standard port)
- `8000` - Django backend (standard Django dev port)
- `3001` - Next.js frontend (custom port)

#### Potential Local Conflicts:
- PostgreSQL (5432): May conflict with existing local PostgreSQL
- Redis (6379): May conflict with existing local Redis
- Django (8000): May conflict with other development servers
- Next.js (3001): Minimal conflict risk

## Transition Implementation Plan

### Phase 1: Pre-Transition Analysis ✅ COMPLETED
- [x] Identify all Docker-related files
- [x] Analyze current service architecture
- [x] Document environment variable requirements
- [x] Assess port conflicts and dependencies

### Phase 2: Docker Removal (Next Steps)
1. **Remove Docker Files**:
   - Remove all Dockerfile and docker-compose files
   - Remove Docker-specific scripts and utilities
   - Remove .dockerignore files

2. **Clean Package Scripts**:
   - Update root package.json scripts
   - Remove Docker references from documentation

### Phase 3: Local Environment Configuration
1. **Backend Configuration**:
   - Create new `.env.local` for local development
   - Update database connection strings
   - Configure local Redis connection
   - Update CORS settings for localhost

2. **Frontend Configuration**:
   - Update API proxy configuration
   - Ensure proper localhost API endpoints
   - Configure development environment variables

### Phase 4: Local Services Setup
1. **macOS Service Installation**:
   - PostgreSQL via Homebrew
   - Redis via Homebrew
   - Python virtual environment setup
   - Node.js/pnpm dependency management

2. **Database Migration**:
   - Create local PostgreSQL database
   - Run Django migrations
   - Create founder user account
   - Import any existing data

### Phase 5: Testing and Validation
1. **Service Connectivity**:
   - Test PostgreSQL connection
   - Test Redis connection
   - Test API endpoints
   - Test frontend-backend communication

2. **Feature Validation**:
   - Verify founder dashboard functionality
   - Test unified client/project creation workflow
   - Validate authentication system
   - Confirm custom branding elements

## Risk Assessment

### High Risk:
- Database connection failures
- Redis connectivity issues
- CORS configuration problems
- Missing environment variables

### Medium Risk:
- Port conflicts with existing services
- Static file serving issues
- Celery task queue configuration
- Development workflow disruption

### Low Risk:
- Frontend build process
- Package dependency conflicts
- Documentation updates

## Success Criteria

### Technical Requirements:
- [x] All Docker dependencies removed
- [ ] Local PostgreSQL and Redis running
- [ ] Backend API accessible at localhost:8000
- [ ] Frontend accessible at localhost:3001
- [ ] Database migrations successful
- [ ] Authentication system functional

### Functional Requirements:
- [ ] Founder dashboard loads correctly
- [ ] <NAME_EMAIL>/demo123 works
- [ ] Client/project creation workflow functional
- [ ] Custom branding elements preserved
- [ ] All API endpoints responding correctly

## Detailed Implementation Plan

### Phase 2: Docker Removal - File List
**Files to Remove (17 total):**
1. `docker-compose.yml`
2. `docker/docker-compose.yml`
3. `docker-compose.prod.yml`
4. `docker/Dockerfile.backend`
5. `docker/Dockerfile.frontend`
6. `apps/backend/Dockerfile.prod`
7. `apps/frontend/Dockerfile.prod`
8. `apps/frontend/.dockerignore`
9. `docker/start-backend.sh`
10. `docker/init-db.sql`
11. `scripts/monitor.sh`
12. `scripts/deploy.sh`
13. `apps/backend/.env.docker`
14. `docker_console_fix.py`
15. `.env.production`
16. `find_dashboard_references.py` (Docker-dependent utility)
17. Entire `docker/` directory

### Phase 3: Local Environment Files to Create
**New Configuration Files:**
1. `apps/backend/.env.local` - Local development environment
2. `apps/frontend/.env.local` - Frontend local configuration
3. `scripts/setup-local.sh` - Local services setup script
4. `scripts/start-local.sh` - Local development startup script
5. `LOCAL_DEVELOPMENT_GUIDE.md` - Setup instructions

### Phase 4: macOS Service Installation Commands
```bash
# Install PostgreSQL
brew install postgresql@15
brew services start postgresql@15

# Install Redis
brew install redis
brew services start redis

# Create database
createdb mtbrmg_erp

# Setup Python virtual environment
cd apps/backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Setup Node.js dependencies
cd ../../
pnpm install
```

### Phase 5: Local Development Workflow
```bash
# Terminal 1: Start PostgreSQL & Redis (if not auto-started)
brew services start postgresql@15
brew services start redis

# Terminal 2: Start Django Backend
cd apps/backend
source venv/bin/activate
python manage.py migrate
python manage.py create_founder
python manage.py runserver 8000

# Terminal 3: Start Next.js Frontend
cd apps/frontend
pnpm dev

# Access Application
# Frontend: http://localhost:3001/founder-dashboard
# Backend API: http://localhost:8000/api/
# Admin: http://localhost:8000/admin/
```

## Next Steps

1. **Execute Docker Removal** (Phase 2) - Remove all Docker files
2. **Configure Local Environment** (Phase 3) - Create local config files
3. **Install and Setup Local Services** (Phase 4) - Setup PostgreSQL/Redis
4. **Test and Validate** (Phase 5) - Verify functionality
5. **Update Documentation** (Final) - Update README and guides

---

**Analysis Date**: Current
**System Version**: MTBRMG ERP v1.0.0
**Transition Target**: macOS Local Development Environment
**Status**: Ready for Implementation

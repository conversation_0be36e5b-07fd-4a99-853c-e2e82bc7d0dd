#!/bin/bash

# MTBRMG ERP - API Connectivity Test Script
# Tests API connectivity from different domains and provides diagnostics

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 MTBRMG ERP API Connectivity Test${NC}"
echo "=================================================="

# Test URLs
URLS=(
    "http://localhost:8000/api/"
    "http://api.mtbrmg.local:8000/api/"
    "http://127.0.0.1:8000/api/"
)

# Test each URL
for url in "${URLS[@]}"; do
    echo -e "\n${YELLOW}Testing: ${url}${NC}"
    
    # Test connectivity
    if curl -s --connect-timeout 5 --max-time 10 -o /dev/null -w "%{http_code}" "$url" > /tmp/status_code 2>/dev/null; then
        status_code=$(cat /tmp/status_code)
        if [[ "$status_code" == "200" || "$status_code" == "401" || "$status_code" == "403" ]]; then
            echo -e "  ${GREEN}✅ Reachable (HTTP $status_code)${NC}"
        else
            echo -e "  ${RED}❌ Unexpected status: $status_code${NC}"
        fi
    else
        echo -e "  ${RED}❌ Connection failed${NC}"
    fi
    
    # Test DNS resolution for custom domains
    if [[ "$url" == *"mtbrmg.local"* ]]; then
        echo -e "  ${BLUE}🔍 DNS Resolution Test:${NC}"
        if nslookup api.mtbrmg.local 127.0.0.1 > /dev/null 2>&1; then
            echo -e "    ${GREEN}✅ DNS resolves correctly${NC}"
        else
            echo -e "    ${YELLOW}⚠️  DNS resolution via dnsmasq failed, checking /etc/hosts...${NC}"
            if grep -q "api.mtbrmg.local" /etc/hosts; then
                echo -e "    ${GREEN}✅ Found in /etc/hosts${NC}"
            else
                echo -e "    ${RED}❌ Not found in /etc/hosts${NC}"
            fi
        fi
    fi
done

echo -e "\n${BLUE}🔍 CORS Test${NC}"
echo "=================================================="

# Test CORS from frontend domain
echo -e "${YELLOW}Testing CORS from mtbrmg.local:3001...${NC}"

# Create a simple CORS test
cat > /tmp/cors_test.html <<EOF
<!DOCTYPE html>
<html>
<head>
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test</h1>
    <div id="result"></div>
    <script>
        fetch('http://api.mtbrmg.local:8000/api/', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            document.getElementById('result').innerHTML = 
                '<span style="color: green;">✅ CORS working! Status: ' + response.status + '</span>';
        })
        .catch(error => {
            document.getElementById('result').innerHTML = 
                '<span style="color: red;">❌ CORS failed: ' + error.message + '</span>';
        });
    </script>
</body>
</html>
EOF

echo -e "  ${BLUE}📄 CORS test file created at: /tmp/cors_test.html${NC}"
echo -e "  ${BLUE}💡 Open this file in your browser to test CORS manually${NC}"

echo -e "\n${BLUE}🔍 Network Diagnostics${NC}"
echo "=================================================="

# Check if services are running
echo -e "${YELLOW}Service Status:${NC}"

# Check backend
if lsof -i :8000 > /dev/null 2>&1; then
    echo -e "  ${GREEN}✅ Backend (port 8000): Running${NC}"
else
    echo -e "  ${RED}❌ Backend (port 8000): Not running${NC}"
fi

# Check frontend
if lsof -i :3001 > /dev/null 2>&1; then
    echo -e "  ${GREEN}✅ Frontend (port 3001): Running${NC}"
else
    echo -e "  ${RED}❌ Frontend (port 3001): Not running${NC}"
fi

# Check nginx
if lsof -i :80 > /dev/null 2>&1; then
    echo -e "  ${GREEN}✅ Nginx (port 80): Running${NC}"
else
    echo -e "  ${YELLOW}⚠️  Nginx (port 80): Not running${NC}"
fi

echo -e "\n${BLUE}🔍 Environment Check${NC}"
echo "=================================================="

# Check environment variables
echo -e "${YELLOW}Frontend Environment:${NC}"
if [[ -f "apps/frontend/.env.local" ]]; then
    echo -e "  ${GREEN}✅ .env.local exists${NC}"
    echo -e "  ${BLUE}API URL:${NC} $(grep NEXT_PUBLIC_API_URL apps/frontend/.env.local | head -1)"
else
    echo -e "  ${RED}❌ .env.local missing${NC}"
fi

echo -e "\n${YELLOW}Backend Environment:${NC}"
if [[ -f "apps/backend/.env" ]]; then
    echo -e "  ${GREEN}✅ .env exists${NC}"
    echo -e "  ${BLUE}CORS Origins:${NC} $(grep CORS_ALLOWED_ORIGINS apps/backend/.env)"
else
    echo -e "  ${RED}❌ .env missing${NC}"
fi

echo -e "\n${BLUE}💡 Troubleshooting Tips${NC}"
echo "=================================================="
echo -e "1. ${YELLOW}If custom domains don't work:${NC}"
echo -e "   - Use fallback: http://localhost:3001 and http://localhost:8000"
echo -e "   - Run: ./scripts/setup-local-domains.sh"
echo -e ""
echo -e "2. ${YELLOW}If CORS errors occur:${NC}"
echo -e "   - Check backend CORS_ALLOWED_ORIGINS in apps/backend/.env"
echo -e "   - Restart backend: ./scripts/mtbrmg-dev.sh restart backend"
echo -e ""
echo -e "3. ${YELLOW}If API is slow:${NC}"
echo -e "   - Check network connectivity"
echo -e "   - Use localhost instead of custom domains"
echo -e "   - Check DNS resolution"

# Cleanup
rm -f /tmp/status_code

echo -e "\n${GREEN}🎯 Test completed!${NC}"

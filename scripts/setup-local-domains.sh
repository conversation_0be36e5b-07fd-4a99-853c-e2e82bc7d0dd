#!/bin/bash

# MTBRMG ERP - Professional Local Development Setup
# This script sets up professional local domains for development

echo "🚀 Setting up professional local development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if running on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS only"
    exit 1
fi

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    print_error "Homebrew is required but not installed"
    print_info "Install Homebrew from: https://brew.sh"
    exit 1
fi

# Install dnsmasq if not already installed
if ! brew list dnsmasq &> /dev/null; then
    print_info "Installing dnsmasq..."
    brew install dnsmasq
else
    print_status "dnsmasq is already installed"
fi

# Create dnsmasq configuration
print_info "Configuring dnsmasq for .local domains..."

# Create dnsmasq config directory if it doesn't exist
sudo mkdir -p /opt/homebrew/etc/dnsmasq.d

# Create MTBRMG-specific dnsmasq configuration
sudo tee /opt/homebrew/etc/dnsmasq.d/mtbrmg.conf > /dev/null <<EOF
# MTBRMG ERP Local Development Configuration
# All .mtbrmg domains will resolve to localhost

# Main domain
address=/mtbrmg.local/127.0.0.1

# Subdomains for different services
address=/api.mtbrmg.local/127.0.0.1
address=/admin.mtbrmg.local/127.0.0.1
address=/app.mtbrmg.local/127.0.0.1
address=/dashboard.mtbrmg.local/127.0.0.1

# Development environments
address=/dev.mtbrmg.local/127.0.0.1
address=/staging.mtbrmg.local/127.0.0.1
address=/test.mtbrmg.local/127.0.0.1

# Listen on localhost only
listen-address=127.0.0.1

# Use port 53 for DNS
port=53

# Don't read /etc/hosts
no-hosts

# Don't poll /etc/resolv.conf
no-poll

# Cache size
cache-size=1000
EOF

# Update main dnsmasq configuration
if ! grep -q "conf-dir=/opt/homebrew/etc/dnsmasq.d" /opt/homebrew/etc/dnsmasq.conf; then
    echo "conf-dir=/opt/homebrew/etc/dnsmasq.d" | sudo tee -a /opt/homebrew/etc/dnsmasq.conf > /dev/null
fi

# Create resolver directory
sudo mkdir -p /etc/resolver

# Create resolver for .mtbrmg.local domains
sudo tee /etc/resolver/mtbrmg.local > /dev/null <<EOF
nameserver 127.0.0.1
port 53
EOF

# Start and enable dnsmasq
print_info "Starting dnsmasq service..."
sudo brew services start dnsmasq

# Add backup /etc/hosts entries (fallback)
print_info "Adding backup entries to /etc/hosts..."

# Create backup of hosts file
sudo cp /etc/hosts /etc/hosts.backup.$(date +%Y%m%d_%H%M%S)

# Add MTBRMG entries to hosts file if they don't exist
if ! grep -q "# MTBRMG ERP Local Development" /etc/hosts; then
    sudo tee -a /etc/hosts > /dev/null <<EOF

# MTBRMG ERP Local Development
127.0.0.1    mtbrmg.local
127.0.0.1    api.mtbrmg.local
127.0.0.1    admin.mtbrmg.local
127.0.0.1    app.mtbrmg.local
127.0.0.1    dashboard.mtbrmg.local
127.0.0.1    dev.mtbrmg.local
127.0.0.1    staging.mtbrmg.local
127.0.0.1    test.mtbrmg.local
EOF
fi

# Test DNS resolution
print_info "Testing DNS resolution..."
if nslookup mtbrmg.local 127.0.0.1 > /dev/null 2>&1; then
    print_status "DNS resolution working correctly"
else
    print_warning "DNS resolution test failed, but /etc/hosts fallback is in place"
fi

# Create nginx configuration for reverse proxy (optional)
print_info "Creating nginx configuration template..."
mkdir -p config/nginx

cat > config/nginx/mtbrmg.conf <<EOF
# MTBRMG ERP - Nginx Local Development Configuration
# Place this file in your nginx sites-enabled directory

# Frontend (Next.js)
server {
    listen 80;
    server_name mtbrmg.local app.mtbrmg.local dashboard.mtbrmg.local;
    
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# Backend API (Django)
server {
    listen 80;
    server_name api.mtbrmg.local admin.mtbrmg.local;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location /static/ {
        alias /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/staticfiles/;
    }
    
    location /media/ {
        alias /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/media/;
    }
}
EOF

print_status "Setup completed successfully!"
print_info "Available domains:"
echo "  🌐 Frontend: http://mtbrmg.local"
echo "  🌐 API: http://api.mtbrmg.local"
echo "  🌐 Admin: http://admin.mtbrmg.local"
echo "  🌐 Dashboard: http://dashboard.mtbrmg.local"
echo ""
print_info "Next steps:"
echo "  1. Update your Django ALLOWED_HOSTS setting"
echo "  2. Update your Next.js API configuration"
echo "  3. Optional: Install and configure nginx for reverse proxy"
echo ""
print_warning "Note: You may need to restart your browser to clear DNS cache"

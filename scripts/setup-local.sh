#!/bin/bash

# MTBRMG ERP Local Development Setup Script
# This script sets up the local development environment on macOS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Homebrew is installed
check_homebrew() {
    log "Checking for Homebrew..."
    if ! command -v brew &> /dev/null; then
        error "Homebrew is not installed. Please install it first:"
        echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
    success "Homebrew is installed"
}

# Install PostgreSQL
install_postgresql() {
    log "Installing PostgreSQL..."

    # Check for existing PostgreSQL installations
    if brew services list | grep -q "postgresql.*started"; then
        success "PostgreSQL is already running"
    elif brew list postgresql@15 &> /dev/null; then
        log "Starting PostgreSQL 15 service..."
        brew services start postgresql@15 || {
            warning "PostgreSQL 15 failed to start, trying to restart..."
            brew services restart postgresql@15
        }
        success "PostgreSQL 15 service started"
    elif brew list postgresql@14 &> /dev/null; then
        log "Using existing PostgreSQL 14..."
        brew services start postgresql@14 || brew services restart postgresql@14
        success "PostgreSQL 14 service started"
    else
        log "Installing PostgreSQL 15..."
        brew install postgresql@15
        brew services start postgresql@15
        success "PostgreSQL 15 installed and started"
    fi
    
    # Wait for PostgreSQL to be ready
    log "Waiting for PostgreSQL to be ready..."
    sleep 3
    
    # Create database
    log "Creating MTBRMG ERP database..."
    if createdb mtbrmg_erp 2>/dev/null; then
        success "Database 'mtbrmg_erp' created"
    else
        warning "Database 'mtbrmg_erp' may already exist"
    fi
}

# Install Redis
install_redis() {
    log "Installing Redis..."
    
    if brew list redis &> /dev/null; then
        success "Redis is already installed"
    else
        brew install redis
        success "Redis installed"
    fi
    
    # Start Redis service
    log "Starting Redis service..."
    brew services start redis
    success "Redis service started"
}

# Setup Python environment
setup_python_env() {
    log "Setting up Python virtual environment..."
    
    cd apps/backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        success "Virtual environment created"
    else
        success "Virtual environment already exists"
    fi
    
    # Activate virtual environment and install dependencies
    source venv/bin/activate
    log "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    success "Python dependencies installed"
    
    cd ../..
}

# Setup Node.js environment
setup_node_env() {
    log "Setting up Node.js environment..."
    
    # Check if pnpm is installed
    if ! command -v pnpm &> /dev/null; then
        log "Installing pnpm..."
        npm install -g pnpm
        success "pnpm installed"
    else
        success "pnpm is already installed"
    fi
    
    # Install dependencies
    log "Installing Node.js dependencies..."
    pnpm install
    success "Node.js dependencies installed"
}

# Run database migrations
setup_database() {
    log "Setting up database..."
    
    cd apps/backend
    source venv/bin/activate
    
    # Run migrations
    log "Running database migrations..."
    python manage.py makemigrations
    python manage.py migrate
    success "Database migrations completed"
    
    # Update database configuration for current user
    log "Updating database configuration..."
    CURRENT_USER=$(whoami)
    sed -i '' "s/DB_USER=postgres/DB_USER=$CURRENT_USER/" .env.local
    sed -i '' "s/DB_PASSWORD=postgres/DB_PASSWORD=/" .env.local
    cp .env.local .env

    # Create founder user
    log "Creating founder user..."
    python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='founder').exists():
    User.objects.create_superuser('founder', '<EMAIL>', 'demo123')
    print('Founder user created successfully')
else:
    print('Founder user already exists')
"
    success "Founder user setup completed"
    
    cd ../..
}

# Main setup function
main() {
    log "Starting MTBRMG ERP Local Development Setup..."
    echo "=================================================="
    
    check_homebrew
    install_postgresql
    install_redis
    setup_python_env
    setup_node_env
    setup_database
    
    echo "=================================================="
    success "MTBRMG ERP Local Development Setup Complete!"
    echo ""
    log "Next steps:"
    echo "  1. Run './scripts/start-local.sh' to start the development servers"
    echo "  2. Access the application at http://localhost:3001/founder-dashboard"
    echo "  3. Login with: <EMAIL> / demo123"
    echo ""
    log "Services installed and running:"
    echo "  - PostgreSQL: localhost:5432"
    echo "  - Redis: localhost:6379"
    echo "  - Backend API: http://localhost:8000"
    echo "  - Frontend: http://localhost:3001"
}

# Run main function
main "$@"

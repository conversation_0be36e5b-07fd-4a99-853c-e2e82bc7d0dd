#!/bin/bash

# Hybrid development: Production build + file watching
export NODE_OPTIONS="--max-old-space-size=4096"

echo "🔥 Starting HYBRID development (Prod build + Hot reload)..."

# Function to rebuild on changes
rebuild_frontend() {
    echo "🔄 Rebuilding frontend..."
    cd apps/frontend
    NEXT_CONFIG_FILE=next.config.prod.mjs pnpm build > /dev/null 2>&1
    echo "✅ Frontend rebuilt!"
}

# Start backend
cd apps/backend
source venv/bin/activate
python manage.py runserver 8000 &
BACKEND_PID=$!

# Initial frontend build
cd ../frontend
NEXT_CONFIG_FILE=next.config.prod.mjs pnpm build

# Start production frontend
pnpm start &
FRONTEND_PID=$!

# Watch for changes and rebuild
echo "👀 Watching for changes..."
fswatch -o app/ components/ lib/ | while read f; do
    rebuild_frontend
    # Restart frontend
    kill $FRONTEND_PID
    pnpm start &
    FRONTEND_PID=$!
done &
WATCHER_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Frontend PID: $FRONTEND_PID"
echo "✅ Watcher PID: $WATCHER_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID $WATCHER_PID; exit" INT
wait

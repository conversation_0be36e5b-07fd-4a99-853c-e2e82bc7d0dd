#!/bin/bash

# MTBRMG ERP Local Development Startup Script
# This script starts all services for local development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if services are running
check_services() {
    log "Checking local services..."
    
    # Check PostgreSQL
    if pg_isready -h localhost -p 5432 &> /dev/null; then
        success "PostgreSQL is running"
    else
        warning "PostgreSQL is not running. Starting..."
        # Try different PostgreSQL versions
        if brew list postgresql@15 &> /dev/null; then
            brew services start postgresql@15
        elif brew list postgresql@14 &> /dev/null; then
            brew services start postgresql@14
        else
            brew services start postgresql
        fi
        sleep 3
        if pg_isready -h localhost -p 5432 &> /dev/null; then
            success "PostgreSQL started successfully"
        else
            error "Failed to start PostgreSQL"
            exit 1
        fi
    fi
    
    # Check Redis
    if redis-cli -h localhost -p 6379 ping &> /dev/null; then
        success "Redis is running"
    else
        warning "Redis is not running. Starting..."
        brew services start redis
        sleep 2
        if redis-cli -h localhost -p 6379 ping &> /dev/null; then
            success "Redis started successfully"
        else
            error "Failed to start Redis"
            exit 1
        fi
    fi
}

# Start backend server
start_backend() {
    log "Starting Django backend server..."
    
    cd apps/backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        error "Virtual environment not found. Please run './scripts/setup-local.sh' first"
        exit 1
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Run migrations (in case there are new ones)
    python manage.py migrate --run-syncdb
    
    # Collect static files
    python manage.py collectstatic --noinput --clear
    
    # Start the server
    log "Django backend starting at http://localhost:8000"
    python manage.py runserver 8000 &
    BACKEND_PID=$!
    
    cd ../..
    
    # Wait for backend to be ready
    log "Waiting for backend to be ready..."
    for i in {1..30}; do
        if curl -f http://localhost:8000/api/health/ &> /dev/null; then
            success "Backend is ready at http://localhost:8000"
            break
        fi
        sleep 1
    done
}

# Start frontend server
start_frontend() {
    log "Starting Next.js frontend server..."
    
    cd apps/frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        warning "Node modules not found. Installing dependencies..."
        pnpm install
    fi
    
    # Start the frontend server
    log "Next.js frontend starting at http://localhost:3001"
    pnpm dev &
    FRONTEND_PID=$!
    
    cd ../..
    
    # Wait for frontend to be ready
    log "Waiting for frontend to be ready..."
    for i in {1..60}; do
        if curl -f http://localhost:3001 &> /dev/null; then
            success "Frontend is ready at http://localhost:3001"
            break
        fi
        sleep 1
    done
}

# Display status
show_status() {
    echo ""
    echo "=================================================="
    success "MTBRMG ERP Local Development Environment Started!"
    echo "=================================================="
    echo ""
    log "Services Status:"
    echo "  ✅ PostgreSQL: localhost:5432"
    echo "  ✅ Redis: localhost:6379"
    echo "  ✅ Backend API: http://localhost:8000"
    echo "  ✅ Frontend: http://localhost:3001"
    echo ""
    log "Application Access:"
    echo "  🌐 Founder Dashboard: http://localhost:3001/founder-dashboard"
    echo "  🔐 Login: <EMAIL> / demo123"
    echo "  ⚙️  Admin Panel: http://localhost:8000/admin/"
    echo "  📡 API Docs: http://localhost:8000/api/"
    echo ""
    log "Development Tools:"
    echo "  📊 Database: psql -h localhost -p 5432 -U postgres -d mtbrmg_erp"
    echo "  🔧 Redis CLI: redis-cli -h localhost -p 6379"
    echo ""
    warning "Press Ctrl+C to stop all services"
}

# Cleanup function
cleanup() {
    log "Stopping development servers..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes
    pkill -f "python manage.py runserver" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true
    
    success "Development servers stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main function
main() {
    log "Starting MTBRMG ERP Local Development Environment..."
    
    check_services
    start_backend
    start_frontend
    show_status
    
    # Keep the script running
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"

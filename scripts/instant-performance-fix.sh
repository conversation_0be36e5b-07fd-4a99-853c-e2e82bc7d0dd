#!/bin/bash

# MTBRMG ERP - Instant Performance Fix
# Immediate optimizations for slow macOS development

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}⚡ INSTANT PERFORMANCE FIX${NC}"
echo "=================================="

# 1. Kill unnecessary processes
echo -e "${YELLOW}🔥 Killing resource-heavy processes...${NC}"
pkill -f "Google Chrome Helper" 2>/dev/null || true
pkill -f "Spotlight" 2>/dev/null || true
pkill -f "mds" 2>/dev/null || true

# 2. Optimize Node.js memory
echo -e "${YELLOW}🧠 Optimizing Node.js memory...${NC}"
export NODE_OPTIONS="--max-old-space-size=4096 --max-semi-space-size=256"
echo "export NODE_OPTIONS='--max-old-space-size=4096 --max-semi-space-size=256'" >> ~/.zshrc

# 3. Disable Next.js telemetry and optimizations
echo -e "${YELLOW}📊 Disabling Next.js telemetry...${NC}"
npx next telemetry disable

# 4. Create optimized environment
cat > apps/frontend/.env.performance <<EOF
# Performance optimizations
NEXT_TELEMETRY_DISABLED=1
NODE_ENV=development
NEXT_PRIVATE_STANDALONE=true
NEXT_PRIVATE_DEBUG_CACHE=false
TURBO_TEAM=mtbrmg
TURBO_API=http://127.0.0.1:9080
EOF

# 5. Optimize database connections
echo -e "${YELLOW}🗄️ Optimizing database...${NC}"
psql -h localhost -U muhammadyoussef -d mtbrmg_erp -c "
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
" 2>/dev/null || echo "Database optimization skipped"

# 6. Create lightweight development script
cat > scripts/fast-dev.sh <<'EOF'
#!/bin/bash

# Ultra-fast development mode
export NODE_OPTIONS="--max-old-space-size=4096"
export NEXT_TELEMETRY_DISABLED=1

echo "🚀 Starting ULTRA-FAST development mode..."

# Start backend with optimizations
cd apps/backend
source venv/bin/activate
python manage.py runserver 8000 --noreload &
BACKEND_PID=$!

# Start frontend with optimizations
cd ../frontend
NEXT_TELEMETRY_DISABLED=1 pnpm dev --turbo &
FRONTEND_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Frontend PID: $FRONTEND_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

chmod +x scripts/fast-dev.sh

echo -e "${GREEN}✅ Instant optimizations applied!${NC}"
echo -e "${BLUE}🚀 Use: ./scripts/fast-dev.sh for ultra-fast development${NC}"

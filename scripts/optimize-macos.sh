#!/bin/bash

# MTBRMG ERP - macOS Development Optimization
# Optimize macOS settings for faster development

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔥 Optimizing macOS for Development${NC}"
echo "====================================="

# 1. Disable Spotlight indexing for development folders
echo -e "${YELLOW}🔍 Disabling Spotlight for development folders...${NC}"
sudo mdutil -i off /Users/<USER>/Sites/mtbrmg-erp-system/node_modules 2>/dev/null || true
sudo mdutil -i off /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/node_modules 2>/dev/null || true
sudo mdutil -i off /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/venv 2>/dev/null || true

# 2. Exclude from Time Machine
echo -e "${YELLOW}⏰ Excluding development folders from Time Machine...${NC}"
tmutil addexclusion /Users/<USER>/Sites/mtbrmg-erp-system/node_modules 2>/dev/null || true
tmutil addexclusion /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend/node_modules 2>/dev/null || true
tmutil addexclusion /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/venv 2>/dev/null || true

# 3. Increase file descriptor limits
echo -e "${YELLOW}📁 Increasing file descriptor limits...${NC}"
echo "kern.maxfiles=65536" | sudo tee -a /etc/sysctl.conf > /dev/null || true
echo "kern.maxfilesperproc=32768" | sudo tee -a /etc/sysctl.conf > /dev/null || true

# Add to shell profile
cat >> ~/.zshrc <<'EOF'

# Development optimizations
ulimit -n 32768
export NODE_OPTIONS="--max-old-space-size=4096"
export NEXT_TELEMETRY_DISABLED=1
EOF

# 4. Optimize DNS
echo -e "${YELLOW}🌐 Optimizing DNS settings...${NC}"
sudo networksetup -setdnsservers Wi-Fi ******* ******* 2>/dev/null || true

# 5. Disable unnecessary services
echo -e "${YELLOW}🚫 Disabling unnecessary services...${NC}"
launchctl unload -w /System/Library/LaunchAgents/com.apple.photoanalysisd.plist 2>/dev/null || true
launchctl unload -w /System/Library/LaunchDaemons/com.apple.metadata.mds.plist 2>/dev/null || true

# 6. Create performance monitoring script
cat > scripts/monitor-performance.sh <<'EOF'
#!/bin/bash

# Real-time performance monitoring
echo "🔍 MTBRMG ERP Performance Monitor"
echo "================================="

while true; do
    clear
    echo "🔍 MTBRMG ERP Performance Monitor"
    echo "================================="
    echo "⏰ $(date)"
    echo ""
    
    # System load
    echo "📊 System Load:"
    uptime
    echo ""
    
    # Memory usage
    echo "🧠 Memory Usage:"
    vm_stat | head -5
    echo ""
    
    # Process monitoring
    echo "🔥 Top Processes:"
    ps aux | head -10
    echo ""
    
    # Development processes
    echo "💻 Development Processes:"
    ps aux | grep -E "(node|python|postgres|redis)" | grep -v grep
    echo ""
    
    echo "Press Ctrl+C to exit"
    sleep 5
done
EOF

chmod +x scripts/monitor-performance.sh

# 7. Create system cleanup script
cat > scripts/cleanup-system.sh <<'EOF'
#!/bin/bash

echo "🧹 Cleaning up system for better performance..."

# Clear caches
sudo rm -rf /System/Library/Caches/*
rm -rf ~/Library/Caches/*
rm -rf /tmp/*

# Clear logs
sudo rm -rf /var/log/*
rm -rf ~/Library/Logs/*

# Clear development caches
rm -rf node_modules/.cache
rm -rf apps/frontend/.next
rm -rf apps/frontend/node_modules/.cache

echo "✅ System cleanup completed!"
EOF

chmod +x scripts/cleanup-system.sh

echo -e "${GREEN}✅ macOS optimization completed!${NC}"
echo -e "${BLUE}📊 Monitor performance: ./scripts/monitor-performance.sh${NC}"
echo -e "${BLUE}🧹 Clean system: ./scripts/cleanup-system.sh${NC}"
echo -e "${YELLOW}⚠️  Please restart your terminal for changes to take effect${NC}"

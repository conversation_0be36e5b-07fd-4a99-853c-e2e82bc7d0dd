#!/bin/bash

# Real-time performance monitoring
echo "🔍 MTBRMG ERP Performance Monitor"
echo "================================="

while true; do
    clear
    echo "🔍 MTBRMG ERP Performance Monitor"
    echo "================================="
    echo "⏰ $(date)"
    echo ""
    
    # System load
    echo "📊 System Load:"
    uptime
    echo ""
    
    # Memory usage
    echo "🧠 Memory Usage:"
    vm_stat | head -5
    echo ""
    
    # Process monitoring
    echo "🔥 Top Processes:"
    ps aux | head -10
    echo ""
    
    # Development processes
    echo "💻 Development Processes:"
    ps aux | grep -E "(node|python|postgres|redis)" | grep -v grep
    echo ""
    
    echo "Press Ctrl+C to exit"
    sleep 5
done

#!/bin/bash

# MTBRMG ERP - Production-like Local Setup
# Ultra-fast production builds with local development features

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Setting up Production-like Local Development${NC}"
echo "================================================"

# 1. Build optimized frontend
echo -e "${YELLOW}🏗️ Building optimized frontend...${NC}"
cd apps/frontend

# Create production environment for local
cat > .env.production.local <<EOF
# Production-like local environment
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_APP_NAME=MTBRMG ERP
NEXT_PUBLIC_AUTH_URL=http://localhost:8000/api/auth
EOF

# Optimize Next.js config for production-local
cat > next.config.prod.mjs <<'EOF'
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@mui/material', '@mui/icons-material'],
  },
  compiler: {
    removeConsole: false, // Keep console for development
  },
  poweredByHeader: false,
  compress: true,
  swcMinify: true,
  reactStrictMode: false, // Disable for performance
  images: {
    unoptimized: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
}

export default nextConfig
EOF

# Build the application
echo -e "${YELLOW}📦 Building application...${NC}"
NEXT_CONFIG_FILE=next.config.prod.mjs pnpm build

# 2. Create production server script
cat > ../../scripts/prod-local.sh <<'EOF'
#!/bin/bash

# Production-like local development
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=4096"

echo "🚀 Starting PRODUCTION-LIKE local development..."

# Start optimized backend
cd apps/backend
source venv/bin/activate

# Use production settings for Django
export DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings
python manage.py collectstatic --noinput > /dev/null 2>&1
python manage.py runserver 8000 &
BACKEND_PID=$!

# Start production frontend
cd ../frontend
pnpm start &
FRONTEND_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Frontend PID: $FRONTEND_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"
echo "⚡ Running in PRODUCTION mode for maximum speed!"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

chmod +x ../../scripts/prod-local.sh

# 3. Create hybrid development script (production build + hot reload)
cat > ../../scripts/hybrid-dev.sh <<'EOF'
#!/bin/bash

# Hybrid development: Production build + file watching
export NODE_OPTIONS="--max-old-space-size=4096"

echo "🔥 Starting HYBRID development (Prod build + Hot reload)..."

# Function to rebuild on changes
rebuild_frontend() {
    echo "🔄 Rebuilding frontend..."
    cd apps/frontend
    NEXT_CONFIG_FILE=next.config.prod.mjs pnpm build > /dev/null 2>&1
    echo "✅ Frontend rebuilt!"
}

# Start backend
cd apps/backend
source venv/bin/activate
python manage.py runserver 8000 &
BACKEND_PID=$!

# Initial frontend build
cd ../frontend
NEXT_CONFIG_FILE=next.config.prod.mjs pnpm build

# Start production frontend
pnpm start &
FRONTEND_PID=$!

# Watch for changes and rebuild
echo "👀 Watching for changes..."
fswatch -o app/ components/ lib/ | while read f; do
    rebuild_frontend
    # Restart frontend
    kill $FRONTEND_PID
    pnpm start &
    FRONTEND_PID=$!
done &
WATCHER_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Frontend PID: $FRONTEND_PID"
echo "✅ Watcher PID: $WATCHER_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID $WATCHER_PID; exit" INT
wait
EOF

chmod +x ../../scripts/hybrid-dev.sh

# Install fswatch if not available
if ! command -v fswatch &> /dev/null; then
    echo -e "${YELLOW}📦 Installing fswatch for file watching...${NC}"
    brew install fswatch
fi

cd ../..

echo -e "${GREEN}✅ Production-like local setup completed!${NC}"
echo -e "${BLUE}🚀 Ultra-fast production mode: ./scripts/prod-local.sh${NC}"
echo -e "${BLUE}🔥 Hybrid mode (prod + hot reload): ./scripts/hybrid-dev.sh${NC}"
echo -e "${YELLOW}⚡ This should be 5-10x faster than development mode!${NC}"

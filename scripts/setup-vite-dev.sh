#!/bin/bash

# MTBRMG ERP - Ultra-Fast Vite Development Setup
# Replace Next.js with Vite for 10x faster development

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🏎️ Setting up ULTRA-FAST Vite Development${NC}"
echo "=============================================="

# Create Vite configuration
echo -e "${YELLOW}⚡ Creating Vite configuration...${NC}"

cd apps/frontend

# Install Vite and dependencies
echo -e "${YELLOW}📦 Installing Vite dependencies...${NC}"
pnpm add -D vite @vitejs/plugin-react vite-tsconfig-paths

# Create Vite config
cat > vite.config.ts <<'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import path from 'path'

export default defineConfig({
  plugins: [
    react({
      fastRefresh: true,
      babel: {
        plugins: [
          ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
        ]
      }
    }),
    tsconfigPaths()
  ],
  server: {
    port: 3001,
    host: true,
    hmr: {
      overlay: false
    },
    fs: {
      strict: false
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
      '@/components': path.resolve(__dirname, './components'),
      '@/lib': path.resolve(__dirname, './lib'),
      '@/app': path.resolve(__dirname, './app')
    }
  },
  define: {
    'process.env': process.env
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'axios',
      '@tanstack/react-query',
      'zustand'
    ],
    exclude: ['@mtbrmg/shared']
  },
  build: {
    target: 'esnext',
    minify: 'esbuild',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['axios', '@tanstack/react-query']
        }
      }
    }
  },
  esbuild: {
    target: 'esnext',
    format: 'esm'
  }
})
EOF

# Create index.html for Vite
cat > index.html <<'EOF'
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MTBRMG ERP</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
EOF

# Create main.tsx entry point
mkdir -p src
cat > src/main.tsx <<'EOF'
import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import App from '../app/layout'
import '../app/globals.css'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
})

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </QueryClientProvider>
  </React.StrictMode>,
)
EOF

# Update package.json scripts
echo -e "${YELLOW}📝 Updating package.json scripts...${NC}"
cat > package.json.new <<EOF
{
  "name": "@mtbrmg/frontend",
  "version": "1.0.0",
  "private": true,
  "scripts": {
    "dev": "vite --port 3001",
    "dev-next": "next dev --port 3001",
    "build": "vite build",
    "build-next": "next build",
    "preview": "vite preview",
    "start": "next start --port 3001",
    "lint": "next lint",
    "type-check": "tsc --noEmit"
  },
  "dependencies": $(cat package.json | jq '.dependencies'),
  "devDependencies": $(cat package.json | jq '.devDependencies')
}
EOF

mv package.json.new package.json

# Create fast development script
cat > ../../scripts/vite-dev.sh <<'EOF'
#!/bin/bash

# Ultra-fast Vite development
export NODE_OPTIONS="--max-old-space-size=4096"
export VITE_FAST_REFRESH=true

echo "🏎️ Starting ULTRA-FAST Vite development..."

# Start backend
cd apps/backend
source venv/bin/activate
python manage.py runserver 8000 --noreload &
BACKEND_PID=$!

# Start Vite frontend
cd ../frontend
pnpm dev &
FRONTEND_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Vite Frontend PID: $FRONTEND_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF

chmod +x ../../scripts/vite-dev.sh

echo -e "${GREEN}✅ Vite setup completed!${NC}"
echo -e "${BLUE}🚀 Use: ./scripts/vite-dev.sh for ultra-fast development${NC}"
echo -e "${YELLOW}⚡ This should be 10x faster than Next.js!${NC}"

#!/bin/bash

# MTBRMG ERP - Professional Development Management Script
# Advanced local development environment management

set -e

# Configuration
PROJECT_ROOT="/Users/<USER>/Sites/mtbrmg-erp-system"
BACKEND_DIR="$PROJECT_ROOT/apps/backend"
FRONTEND_DIR="$PROJECT_ROOT/apps/frontend"
LOGS_DIR="$PROJECT_ROOT/logs"
PIDS_DIR="$PROJECT_ROOT/.pids"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Create necessary directories
mkdir -p "$LOGS_DIR" "$PIDS_DIR"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# Check if process is running
is_running() {
    local pid_file="$1"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Start PostgreSQL
start_postgres() {
    log_info "Starting PostgreSQL..."
    if brew services list | grep postgresql | grep started > /dev/null; then
        log_success "PostgreSQL is already running"
    else
        brew services start postgresql@15
        sleep 2
        log_success "PostgreSQL started"
    fi
}

# Start Redis
start_redis() {
    log_info "Starting Redis..."
    if brew services list | grep redis | grep started > /dev/null; then
        log_success "Redis is already running"
    else
        brew services start redis
        sleep 2
        log_success "Redis started"
    fi
}

# Start Backend
start_backend() {
    log_info "Starting Django backend..."
    
    if is_running "$PIDS_DIR/backend.pid"; then
        log_warning "Backend is already running"
        return
    fi
    
    cd "$BACKEND_DIR"
    source venv/bin/activate
    
    # Run migrations if needed
    python manage.py migrate --check > /dev/null 2>&1 || {
        log_info "Running database migrations..."
        python manage.py migrate
    }
    
    # Collect static files
    python manage.py collectstatic --noinput > /dev/null 2>&1 || true
    
    # Start server
    nohup python manage.py runserver 8000 > "$LOGS_DIR/backend.log" 2>&1 &
    echo $! > "$PIDS_DIR/backend.pid"
    
    sleep 3
    if is_running "$PIDS_DIR/backend.pid"; then
        log_success "Backend started on http://localhost:8000"
    else
        log_error "Failed to start backend"
        return 1
    fi
}

# Start Frontend
start_frontend() {
    log_info "Starting Next.js frontend..."
    
    if is_running "$PIDS_DIR/frontend.pid"; then
        log_warning "Frontend is already running"
        return
    fi
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if needed
    if [[ ! -d "node_modules" ]]; then
        log_info "Installing frontend dependencies..."
        pnpm install
    fi
    
    # Start server
    nohup pnpm dev > "$LOGS_DIR/frontend.log" 2>&1 &
    echo $! > "$PIDS_DIR/frontend.pid"
    
    sleep 5
    if is_running "$PIDS_DIR/frontend.pid"; then
        log_success "Frontend started on http://localhost:3001"
    else
        log_error "Failed to start frontend"
        return 1
    fi
}

# Start Celery Worker
start_celery() {
    log_info "Starting Celery worker..."
    
    if is_running "$PIDS_DIR/celery.pid"; then
        log_warning "Celery is already running"
        return
    fi
    
    cd "$BACKEND_DIR"
    source venv/bin/activate
    
    nohup celery -A mtbrmg_erp worker -l info > "$LOGS_DIR/celery.log" 2>&1 &
    echo $! > "$PIDS_DIR/celery.pid"
    
    sleep 3
    if is_running "$PIDS_DIR/celery.pid"; then
        log_success "Celery worker started"
    else
        log_error "Failed to start Celery worker"
        return 1
    fi
}

# Stop service
stop_service() {
    local service="$1"
    local pid_file="$PIDS_DIR/${service}.pid"
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        log_info "Stopping $service (PID: $pid)..."
        kill "$pid"
        rm -f "$pid_file"
        log_success "$service stopped"
    else
        log_warning "$service is not running"
    fi
}

# Status check
status() {
    echo -e "${CYAN}=== MTBRMG ERP Development Status ===${NC}"
    echo ""
    
    # Check system services
    echo -e "${BLUE}System Services:${NC}"
    if brew services list | grep postgresql | grep started > /dev/null; then
        echo -e "  PostgreSQL: ${GREEN}✓ Running${NC}"
    else
        echo -e "  PostgreSQL: ${RED}✗ Stopped${NC}"
    fi
    
    if brew services list | grep redis | grep started > /dev/null; then
        echo -e "  Redis: ${GREEN}✓ Running${NC}"
    else
        echo -e "  Redis: ${RED}✗ Stopped${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}Application Services:${NC}"
    
    # Check application services
    for service in backend frontend celery; do
        if is_running "$PIDS_DIR/${service}.pid"; then
            local pid=$(cat "$PIDS_DIR/${service}.pid")
            local service_name=$(echo "$service" | sed 's/./\U&/')
            echo -e "  $service_name: ${GREEN}✓ Running${NC} (PID: $pid)"
        else
            local service_name=$(echo "$service" | sed 's/./\U&/')
            echo -e "  $service_name: ${RED}✗ Stopped${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Available URLs:${NC}"
    echo -e "  Frontend: ${CYAN}http://localhost:3001${NC} | ${CYAN}http://mtbrmg.local${NC}"
    echo -e "  Backend:  ${CYAN}http://localhost:8000${NC} | ${CYAN}http://api.mtbrmg.local${NC}"
    echo -e "  Admin:    ${CYAN}http://localhost:8000/admin${NC} | ${CYAN}http://admin.mtbrmg.local${NC}"
}

# Logs
logs() {
    local service="$1"
    local log_file="$LOGS_DIR/${service}.log"
    
    if [[ -f "$log_file" ]]; then
        tail -f "$log_file"
    else
        log_error "Log file for $service not found"
    fi
}

# Main command handler
case "$1" in
    start)
        case "$2" in
            postgres) start_postgres ;;
            redis) start_redis ;;
            backend) start_backend ;;
            frontend) start_frontend ;;
            celery) start_celery ;;
            all|"")
                start_postgres
                start_redis
                start_backend
                start_frontend
                start_celery
                echo ""
                status
                ;;
            *) log_error "Unknown service: $2" ;;
        esac
        ;;
    stop)
        case "$2" in
            postgres) brew services stop postgresql@15 ;;
            redis) brew services stop redis ;;
            backend|frontend|celery) stop_service "$2" ;;
            all|"")
                stop_service backend
                stop_service frontend
                stop_service celery
                brew services stop postgresql@15
                brew services stop redis
                ;;
            *) log_error "Unknown service: $2" ;;
        esac
        ;;
    restart)
        $0 stop "$2"
        sleep 2
        $0 start "$2"
        ;;
    status)
        status
        ;;
    logs)
        if [[ -z "$2" ]]; then
            log_error "Please specify a service: backend, frontend, or celery"
        else
            logs "$2"
        fi
        ;;
    setup)
        log_info "Setting up professional local domains..."
        bash "$PROJECT_ROOT/scripts/setup-local-domains.sh"
        ;;
    *)
        echo "MTBRMG ERP Development Manager"
        echo ""
        echo "Usage: $0 {start|stop|restart|status|logs|setup} [service]"
        echo ""
        echo "Services: postgres, redis, backend, frontend, celery, all"
        echo ""
        echo "Examples:"
        echo "  $0 start all          # Start all services"
        echo "  $0 stop backend       # Stop backend only"
        echo "  $0 restart frontend   # Restart frontend"
        echo "  $0 status             # Show status"
        echo "  $0 logs backend       # Show backend logs"
        echo "  $0 setup              # Setup professional domains"
        ;;
esac

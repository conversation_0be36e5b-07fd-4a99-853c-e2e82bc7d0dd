#!/bin/bash

# Production-like local development
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=4096"

echo "🚀 Starting PRODUCTION-LIKE local development..."

# Start optimized backend
cd apps/backend
source venv/bin/activate

# Use production settings for Django
export DJANGO_SETTINGS_MODULE=mtbrmg_erp.settings
python manage.py collectstatic --noinput > /dev/null 2>&1
python manage.py runserver 8000 &
BACKEND_PID=$!

# Start production frontend
cd ../frontend
pnpm start &
FRONTEND_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Frontend PID: $FRONTEND_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"
echo "⚡ Running in PRODUCTION mode for maximum speed!"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait

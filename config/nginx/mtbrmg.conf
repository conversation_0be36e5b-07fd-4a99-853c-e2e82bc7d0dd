# MTBRMG ERP - Nginx Local Development Configuration
# Place this file in your nginx sites-enabled directory

# Frontend (Next.js)
server {
    listen 80;
    server_name mtbrmg.local app.mtbrmg.local dashboard.mtbrmg.local;
    
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# Backend API (Django)
server {
    listen 80;
    server_name api.mtbrmg.local admin.mtbrmg.local;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/staticfiles/;
    }
    
    location /media/ {
        alias /Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/media/;
    }
}

# 🚀 الدليل الاحترافي لإدارة localhost على macOS - MTBRMG ERP

## 📋 نظرة عامة

هذا الدليل يوضح أفضل الطرق الاحترافية لإدارة بيئة التطوير المحلية لنظام MTBRMG ERP على macOS.

## 🎯 الطرق الاحترافية المتاحة

### 1. **Custom Domain Names (الطريقة الأفضل)**

بدلاً من استخدام `localhost:3001` و `localhost:8000`، ستستخدم:

```
🌐 Frontend: http://mtbrmg.local
🌐 API: http://api.mtbrmg.local  
🌐 Admin: http://admin.mtbrmg.local
🌐 Dashboard: http://dashboard.mtbrmg.local
```

### 2. **Professional Service Management**

إدارة متقدمة للخدمات مع مراقبة الحالة والسجلات.

### 3. **Nginx Reverse Proxy (اختياري)**

توزيع الطلبات بذكاء وتحسين الأداء.

## 🛠️ التثبيت والإعداد

### الخطوة 1: إعداد Custom Domains

```bash
# تشغيل script الإعداد الاحترافي
./scripts/setup-local-domains.sh
```

هذا الـ script سيقوم بـ:
- ✅ تثبيت وتكوين dnsmasq
- ✅ إنشاء resolver للـ domains المخصصة
- ✅ إضافة backup entries في /etc/hosts
- ✅ اختبار DNS resolution

### الخطوة 2: استخدام Professional Service Manager

```bash
# بدء جميع الخدمات
./scripts/mtbrmg-dev.sh start all

# بدء خدمة محددة
./scripts/mtbrmg-dev.sh start backend
./scripts/mtbrmg-dev.sh start frontend

# إيقاف الخدمات
./scripts/mtbrmg-dev.sh stop all

# مراقبة الحالة
./scripts/mtbrmg-dev.sh status

# عرض السجلات
./scripts/mtbrmg-dev.sh logs backend
```

## 🔧 التكوين المتقدم

### إعداد Nginx (اختياري للمحترفين)

```bash
# تثبيت nginx
brew install nginx

# نسخ التكوين
sudo cp config/nginx/mtbrmg-professional.conf /opt/homebrew/etc/nginx/servers/

# بدء nginx
brew services start nginx
```

### مميزات Nginx Setup:
- 🚀 **Load Balancing**: توزيع الأحمال
- 🔒 **Security Headers**: رؤوس الأمان
- 📊 **Rate Limiting**: تحديد معدل الطلبات
- 🗜️ **Gzip Compression**: ضغط البيانات
- 📱 **CORS Handling**: إدارة CORS بذكاء

## 📊 مراقبة النظام

### عرض حالة جميع الخدمات

```bash
./scripts/mtbrmg-dev.sh status
```

**مثال على الخرج:**
```
=== MTBRMG ERP Development Status ===

System Services:
  PostgreSQL: ✓ Running
  Redis: ✓ Running

Application Services:
  Backend: ✓ Running (PID: 12345)
  Frontend: ✓ Running (PID: 12346)
  Celery: ✓ Running (PID: 12347)

Available URLs:
  Frontend: http://localhost:3001 | http://mtbrmg.local
  Backend:  http://localhost:8000 | http://api.mtbrmg.local
  Admin:    http://localhost:8000/admin | http://admin.mtbrmg.local
```

### مراقبة السجلات في الوقت الفعلي

```bash
# سجلات Backend
./scripts/mtbrmg-dev.sh logs backend

# سجلات Frontend  
./scripts/mtbrmg-dev.sh logs frontend

# سجلات Celery
./scripts/mtbrmg-dev.sh logs celery
```

## 🌐 الـ Domains المتاحة

| Domain | الغرض | Port |
|--------|--------|------|
| `mtbrmg.local` | التطبيق الرئيسي | 3001 |
| `api.mtbrmg.local` | Backend API | 8000 |
| `admin.mtbrmg.local` | لوحة الإدارة | 8000 |
| `dashboard.mtbrmg.local` | لوحة التحكم | 3001 |
| `dev.mtbrmg.local` | أدوات التطوير | - |

## 🔄 سير العمل اليومي

### بدء يوم العمل
```bash
# بدء جميع الخدمات
./scripts/mtbrmg-dev.sh start all

# فتح المتصفح
open http://mtbrmg.local
```

### أثناء التطوير
```bash
# مراقبة السجلات
./scripts/mtbrmg-dev.sh logs backend

# إعادة تشغيل خدمة معينة
./scripts/mtbrmg-dev.sh restart frontend
```

### نهاية يوم العمل
```bash
# إيقاف جميع الخدمات
./scripts/mtbrmg-dev.sh stop all
```

## 🚨 استكشاف الأخطاء

### مشكلة: Custom domains لا تعمل

```bash
# اختبار DNS resolution
nslookup mtbrmg.local 127.0.0.1

# إعادة تشغيل dnsmasq
sudo brew services restart dnsmasq

# مسح DNS cache في المتصفح
# Chrome: chrome://net-internals/#dns
```

### مشكلة: خدمة لا تبدأ

```bash
# فحص السجلات
./scripts/mtbrmg-dev.sh logs [service-name]

# فحص الـ ports المستخدمة
lsof -i :3001 -i :8000 -i :5432 -i :6379
```

### مشكلة: بطء في الاستجابة

```bash
# تفعيل nginx للتسريع
brew services start nginx

# مراقبة استخدام الموارد
top -pid $(cat .pids/backend.pid)
```

## 🎨 التخصيص المتقدم

### إضافة domains جديدة

```bash
# تحرير ملف dnsmasq
sudo nano /opt/homebrew/etc/dnsmasq.d/mtbrmg.conf

# إضافة domain جديد
address=/new-subdomain.mtbrmg.local/127.0.0.1

# إعادة تشغيل dnsmasq
sudo brew services restart dnsmasq
```

### تخصيص ports

```bash
# تحرير متغيرات البيئة
nano apps/frontend/.env.local
nano apps/backend/.env

# إعادة تشغيل الخدمات
./scripts/mtbrmg-dev.sh restart all
```

## 📈 مميزات إضافية

### SSL/HTTPS (للمستقبل)
- إعداد شهادات SSL محلية
- تفعيل HTTPS للتطوير الآمن

### Docker Integration
- دمج مع Docker للخدمات الخارجية
- استخدام Docker Compose للبيئات المعقدة

### CI/CD Integration
- ربط مع GitHub Actions
- تشغيل اختبارات تلقائية

## 🎯 الخلاصة

هذا النظام الاحترافي يوفر لك:

✅ **URLs احترافية** بدلاً من localhost  
✅ **إدارة متقدمة للخدمات** مع مراقبة شاملة  
✅ **أداء محسن** مع nginx وcaching  
✅ **أمان متقدم** مع rate limiting وheaders  
✅ **سهولة الاستخدام** مع scripts تلقائية  
✅ **مراقبة شاملة** للسجلات والحالة  

---

**💡 نصيحة:** احفظ هذا الدليل كمرجع واستخدم الـ scripts المرفقة لإدارة بيئة التطوير بكفاءة عالية.

from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from .views import (
    CustomTokenObtainPairView,
    UserRegistrationView,
    UserViewSet,
    user_profile,
    change_password,
    logout_view,
    setup_2fa,
    confirm_2fa,
    disable_2fa,
    mfa_status
)

app_name = 'authentication'

# Create router for viewsets
router = DefaultRouter()
router.register(r'users', UserViewSet)

urlpatterns = [
    # Authentication endpoints
    path('login/', CustomTokenObtainPairView.as_view(), name='login'),
    path('register/', UserRegistrationView.as_view(), name='register'),
    path('refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('logout/', logout_view, name='logout'),

    # User profile endpoints
    path('profile/', user_profile, name='profile'),
    path('change-password/', change_password, name='change_password'),

    # 2FA endpoints
    path('2fa/setup/', setup_2fa, name='setup_2fa'),
    path('2fa/confirm/', confirm_2fa, name='confirm_2fa'),
    path('2fa/disable/', disable_2fa, name='disable_2fa'),
    path('2fa/status/', mfa_status, name='mfa_status'),

    # Include router URLs
    path('', include(router.urls)),
]

from rest_framework import status, generics, viewsets, filters
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.utils import timezone
from django_ratelimit.decorators import ratelimit
from django_filters.rest_framework import DjangoFilterBackend
from .models import User, SecurityLog
from .services import MFAService, SecurityService, AuditService
from .serializers import (
    UserSerializer,
    UserListSerializer,
    CustomTokenObtainPairSerializer,
    UserRegistrationSerializer,
    UserProfileSerializer,
    PasswordChangeSerializer
)


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom login view that returns user data with tokens"""
    serializer_class = CustomTokenObtainPairSerializer
    permission_classes = [AllowAny]


class UserRegistrationView(generics.CreateAPIView):
    """User registration view"""
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        """Create new user and return tokens"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()

        # Generate tokens for the new user
        refresh = RefreshToken.for_user(user)

        return Response({
            'user': UserSerializer(user).data,
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'message': 'تم إنشاء الحساب بنجاح'
        }, status=status.HTTP_201_CREATED)


@api_view(['GET', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """Get or update user profile"""
    user = request.user

    if request.method == 'GET':
        serializer = UserProfileSerializer(user)
        return Response(serializer.data)

    elif request.method in ['PUT', 'PATCH']:
        partial = request.method == 'PATCH'
        serializer = UserProfileSerializer(user, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'user': serializer.data,
                'message': 'تم تحديث الملف الشخصي بنجاح'
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def change_password(request):
    """Change user password"""
    serializer = PasswordChangeSerializer(data=request.data, context={'request': request})

    if serializer.is_valid():
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()

        return Response({
            'message': 'تم تغيير كلمة المرور بنجاح'
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """Logout user by blacklisting refresh token"""
    try:
        refresh_token = request.data.get('refresh')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()

        # Log security event
        SecurityService.log_security_event(
            user=request.user,
            event_type=SecurityLog.EventType.LOGOUT,
            ip_address=request.META.get('REMOTE_ADDR')
        )

        return Response({
            'message': 'تم تسجيل الخروج بنجاح'
        })
    except Exception as e:
        return Response({
            'error': 'حدث خطأ أثناء تسجيل الخروج'
        }, status=status.HTTP_400_BAD_REQUEST)


# 2FA Endpoints
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def setup_2fa(request):
    """Setup 2FA for user"""
    user = request.user

    if MFAService.is_mfa_enabled(user):
        return Response({
            'error': 'المصادقة الثنائية مفعلة بالفعل'
        }, status=status.HTTP_400_BAD_REQUEST)

    result, error = MFAService.setup_totp_for_user(user)

    if error:
        return Response({'error': error}, status=status.HTTP_400_BAD_REQUEST)

    return Response({
        'qr_code': result['qr_code'],
        'secret_key': result['secret_key'],
        'device_id': result['device_id'],
        'message': 'امسح رمز QR باستخدام تطبيق المصادقة الثنائية'
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def confirm_2fa(request):
    """Confirm 2FA setup"""
    user = request.user
    device_id = request.data.get('device_id')
    token = request.data.get('token')

    if not device_id or not token:
        return Response({
            'error': 'معرف الجهاز والرمز مطلوبان'
        }, status=status.HTTP_400_BAD_REQUEST)

    result = MFAService.confirm_totp_setup(user, device_id, token)

    if result['success']:
        # Log security event
        SecurityService.log_security_event(
            user=user,
            event_type=SecurityLog.EventType.MFA_ENABLED,
            ip_address=request.META.get('REMOTE_ADDR')
        )

        return Response({
            'backup_codes': result['backup_codes'],
            'message': result['message']
        })
    else:
        return Response({
            'error': result['message']
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def disable_2fa(request):
    """Disable 2FA for user"""
    user = request.user
    password = request.data.get('password')

    if not password:
        return Response({
            'error': 'كلمة المرور مطلوبة لإلغاء المصادقة الثنائية'
        }, status=status.HTTP_400_BAD_REQUEST)

    if not user.check_password(password):
        return Response({
            'error': 'كلمة المرور غير صحيحة'
        }, status=status.HTTP_400_BAD_REQUEST)

    MFAService.disable_mfa_for_user(user)

    # Log security event
    SecurityService.log_security_event(
        user=user,
        event_type=SecurityLog.EventType.MFA_DISABLED,
        ip_address=request.META.get('REMOTE_ADDR')
    )

    return Response({
        'message': 'تم إلغاء المصادقة الثنائية بنجاح'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def mfa_status(request):
    """Get MFA status for user"""
    user = request.user

    return Response({
        'mfa_enabled': MFAService.is_mfa_enabled(user),
        'backup_codes_count': MFAService.get_backup_codes_count(user)
    })


class UserViewSet(viewsets.ReadOnlyModelViewSet):
    """User management viewset for listing users"""
    queryset = User.objects.filter(is_active=True).order_by('first_name', 'last_name')
    serializer_class = UserListSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['role', 'status']
    search_fields = ['first_name', 'last_name', 'username', 'email']
    ordering_fields = ['first_name', 'last_name', 'username', 'date_joined']
    ordering = ['first_name', 'last_name']

    def get_queryset(self):
        """Filter queryset based on user permissions"""
        queryset = super().get_queryset()
        user = self.request.user

        # Only show active users
        queryset = queryset.filter(is_active=True)

        # Admins can see all users
        if user.is_admin():
            return queryset

        # Other users can see all users but with limited information
        return queryset

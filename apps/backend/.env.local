# MTBRMG ERP Backend - Local Development Environment
# Copy this file to .env for local development

# Django Settings
DEBUG=True
SECRET_KEY=django-insecure-mtbrmg-erp-local-development-key-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration - Local PostgreSQL
USE_SQLITE=False
DB_NAME=mtbrmg_erp
DB_USER=muhammadyoussef
DB_PASSWORD=
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration - Local Redis
REDIS_URL=redis://localhost:6379/0

# CORS Configuration - Local Development
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://127.0.0.1:3001

# Session Configuration - Extended for development
SESSION_COOKIE_AGE=31536000
SESSION_EXPIRE_AT_BROWSER_CLOSE=False
SESSION_SAVE_EVERY_REQUEST=False
SESSION_COOKIE_SECURE=False

# JWT Configuration - Extended timeouts for development
JWT_ACCESS_TOKEN_HOURS=24
JWT_REFRESH_TOKEN_DAYS=30

# Email Configuration (optional for development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=

# AWS Configuration (optional for development)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=us-east-1

# WhatsApp Business API (optional)
WHATSAPP_API_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=

# Development Settings
DJANGO_LOG_LEVEL=INFO
CELERY_ALWAYS_EAGER=True

# Static Files Configuration
STATIC_URL=/static/
STATIC_ROOT=staticfiles/
MEDIA_URL=/media/
MEDIA_ROOT=media/

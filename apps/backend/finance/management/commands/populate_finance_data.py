from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from finance.models import RevenueStream, Expense, ExpenseCategory, FinancialKPI
from authentication.models import User
from clients.models import Client
from projects.models import Project
from team.models import TeamMember


class Command(BaseCommand):
    help = 'Populate sample financial data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample financial data...')

        # Get or create a user
        user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'محمد',
                'last_name': 'عبد الفتاح',
                'is_active': True,
                'is_staff': True
            }
        )
        if created:
            user.set_password('demo123')
            user.save()

        # Create or get team member for the user
        team_member, created = TeamMember.objects.get_or_create(
            user=user,
            defaults={
                'employee_id': 'EMP001',
                'department': 'management',
                'position': 'مؤسس الشركة',
                'hire_date': timezone.now().date(),
                'salary': Decimal('15000.00')
            }
        )

        # Create expense categories
        categories = [
            {'name': 'رواتب الموظفين', 'description': 'رواتب فريق العمل'},
            {'name': 'التسويق والإعلان', 'description': 'مصاريف التسويق الرقمي'},
            {'name': 'المكتب والمعدات', 'description': 'إيجار المكتب والمعدات'},
            {'name': 'البرمجيات والأدوات', 'description': 'اشتراكات البرمجيات'},
        ]

        for cat_data in categories:
            ExpenseCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )

        # Create sample clients
        clients_data = [
            {'name': 'شركة التقنية المتقدمة', 'email': '<EMAIL>'},
            {'name': 'مؤسسة الإبداع الرقمي', 'email': '<EMAIL>'},
            {'name': 'شركة النجاح التجاري', 'email': '<EMAIL>'},
        ]

        clients = []
        for client_data in clients_data:
            client, created = Client.objects.get_or_create(
                name=client_data['name'],
                defaults={
                    'email': client_data['email'],
                    'phone': '+966501234567',
                    'mood': 'happy',
                    'sales_rep': user
                }
            )
            clients.append(client)

        # Create sample projects
        projects_data = [
            {'name': 'تطوير موقع إلكتروني', 'client': clients[0], 'type': 'website'},
            {'name': 'حملة تسويق رقمي', 'client': clients[1], 'type': 'marketing'},
            {'name': 'تصميم هوية بصرية', 'client': clients[2], 'type': 'website'},
        ]

        projects = []
        for proj_data in projects_data:
            project, created = Project.objects.get_or_create(
                name=proj_data['name'],
                defaults={
                    'client': proj_data['client'],
                    'description': f'مشروع {proj_data["name"]}',
                    'type': proj_data['type'],
                    'status': 'development',
                    'priority': 'medium',
                    'start_date': timezone.now().date(),
                    'budget': Decimal('50000.00'),
                    'project_manager': user
                }
            )
            projects.append(project)

        # Create sample revenue streams
        revenue_data = [
            {
                'title': 'دفعة أولى - تطوير موقع',
                'type': 'project_payment',
                'amount': Decimal('25000.00'),
                'project': projects[0],
                'client': clients[0],
                'status': 'received'
            },
            {
                'title': 'دفعة ثانية - تطوير موقع',
                'type': 'project_payment',
                'amount': Decimal('25000.00'),
                'project': projects[0],
                'client': clients[0],
                'status': 'pending'
            },
            {
                'title': 'حملة تسويق رقمي',
                'type': 'service_fee',
                'amount': Decimal('15000.00'),
                'project': projects[1],
                'client': clients[1],
                'status': 'received'
            },
        ]

        for rev_data in revenue_data:
            RevenueStream.objects.get_or_create(
                title=rev_data['title'],
                defaults={
                    'type': rev_data['type'],
                    'amount': rev_data['amount'],
                    'tax_amount': rev_data['amount'] * Decimal('0.15'),  # 15% tax
                    'net_amount': rev_data['amount'] * Decimal('0.85'),
                    'project': rev_data['project'],
                    'client': rev_data['client'],
                    'status': rev_data['status'],
                    'invoice_date': timezone.now().date(),
                    'due_date': timezone.now().date() + timedelta(days=30),
                    'payment_date': timezone.now().date() if rev_data['status'] == 'received' else None,
                    'sales_rep': user
                }
            )

        # Create sample expenses
        expense_categories = ExpenseCategory.objects.all()
        expense_data = [
            {
                'title': 'راتب مطور أول',
                'type': 'salary',
                'amount': Decimal('8000.00'),
                'category': expense_categories.filter(name__contains='رواتب').first(),
                'status': 'paid'
            },
            {
                'title': 'إعلانات فيسبوك',
                'type': 'marketing',
                'amount': Decimal('2500.00'),
                'category': expense_categories.filter(name__contains='التسويق').first(),
                'status': 'paid'
            },
            {
                'title': 'إيجار المكتب',
                'type': 'office',
                'amount': Decimal('3000.00'),
                'category': expense_categories.filter(name__contains='المكتب').first(),
                'status': 'paid'
            },
        ]

        for exp_data in expense_data:
            Expense.objects.get_or_create(
                title=exp_data['title'],
                defaults={
                    'type': exp_data['type'],
                    'amount': exp_data['amount'],
                    'tax_amount': Decimal('0.00'),
                    'net_amount': exp_data['amount'],
                    'category': exp_data['category'],
                    'status': exp_data['status'],
                    'expense_date': timezone.now().date(),
                    'payment_date': timezone.now().date() if exp_data['status'] == 'paid' else None,
                    'team_member': team_member
                }
            )

        # Create sample KPIs
        current_date = timezone.now().date()
        month_start = current_date.replace(day=1)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        kpi_data = [
            {
                'name': 'نمو الإيرادات الشهرية',
                'type': 'revenue_growth',
                'target_value': Decimal('20.00'),  # 20% growth
                'current_value': Decimal('15.50'),
                'previous_value': Decimal('10.00'),
            },
            {
                'name': 'هامش الربح',
                'type': 'profit_margin',
                'target_value': Decimal('30.00'),  # 30% margin
                'current_value': Decimal('25.80'),
                'previous_value': Decimal('22.50'),
            },
        ]

        for kpi in kpi_data:
            FinancialKPI.objects.get_or_create(
                name=kpi['name'],
                type=kpi['type'],
                period_start=month_start,
                period_end=month_end,
                defaults={
                    'target_value': kpi['target_value'],
                    'current_value': kpi['current_value'],
                    'previous_value': kpi['previous_value'],
                }
            )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample financial data!')
        )

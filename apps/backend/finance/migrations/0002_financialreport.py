# Generated by Django 4.2.9 on 2025-06-04 10:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("finance", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="FinancialReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "title",
                    models.CharField(max_length=200, verbose_name="عنوان التقرير"),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="الوصف"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("profit_loss", "الأرباح والخسائر"),
                            ("cash_flow", "التدفق النقدي"),
                            ("balance_sheet", "الميزانية العمومية"),
                            ("budget_analysis", "تحليل الميزانية"),
                            ("department_performance", "أداء الأقسام"),
                            ("client_profitability", "ربحية العملاء"),
                            ("project_financial", "التقرير المالي للمشاريع"),
                            ("expense_analysis", "تحليل المصروفات"),
                            ("revenue_analysis", "تحليل الإيرادات"),
                        ],
                        max_length=25,
                        verbose_name="نوع التقرير",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "مسودة"),
                            ("generating", "قيد الإنشاء"),
                            ("completed", "مكتمل"),
                            ("failed", "فشل"),
                        ],
                        default="draft",
                        max_length=15,
                        verbose_name="الحالة",
                    ),
                ),
                (
                    "period",
                    models.CharField(
                        choices=[
                            ("daily", "يومي"),
                            ("weekly", "أسبوعي"),
                            ("monthly", "شهري"),
                            ("quarterly", "ربع سنوي"),
                            ("yearly", "سنوي"),
                            ("custom", "مخصص"),
                        ],
                        max_length=15,
                        verbose_name="الفترة",
                    ),
                ),
                ("start_date", models.DateField(verbose_name="تاريخ البداية")),
                ("end_date", models.DateField(verbose_name="تاريخ النهاية")),
                (
                    "report_data",
                    models.JSONField(default=dict, verbose_name="بيانات التقرير"),
                ),
                (
                    "file_path",
                    models.CharField(
                        blank=True, max_length=500, null=True, verbose_name="مسار الملف"
                    ),
                ),
                (
                    "file_size",
                    models.PositiveIntegerField(default=0, verbose_name="حجم الملف"),
                ),
                (
                    "generation_time",
                    models.DurationField(
                        blank=True, null=True, verbose_name="وقت الإنشاء"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "generated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="generated_reports",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="تم الإنشاء بواسطة",
                    ),
                ),
            ],
            options={
                "verbose_name": "التقرير المالي",
                "verbose_name_plural": "التقارير المالية",
                "ordering": ["-created_at"],
            },
        ),
    ]

from django.contrib import admin
from simple_history.admin import SimpleHistoryAdmin
from .models import (
    RevenueStream, Expense, ExpenseCategory, CashFlowProjection,
    Budget, BudgetAllocation, FinancialKPI
)


@admin.register(ExpenseCategory)
class ExpenseCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(RevenueStream)
class RevenueStreamAdmin(SimpleHistoryAdmin):
    list_display = [
        'title', 'type', 'status', 'amount', 'client', 'project',
        'invoice_date', 'due_date', 'payment_date'
    ]
    list_filter = ['type', 'status', 'invoice_date', 'due_date']
    search_fields = ['title', 'description', 'client__name', 'project__name']
    date_hierarchy = 'invoice_date'
    ordering = ['-created_at']
    readonly_fields = ['net_amount', 'is_overdue']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'type', 'status')
        }),
        ('التفاصيل المالية', {
            'fields': ('amount', 'tax_amount', 'net_amount')
        }),
        ('العلاقات', {
            'fields': ('client', 'project', 'sales_rep')
        }),
        ('التواريخ', {
            'fields': ('invoice_date', 'due_date', 'payment_date')
        }),
    )


@admin.register(Expense)
class ExpenseAdmin(SimpleHistoryAdmin):
    list_display = [
        'title', 'type', 'status', 'amount', 'category', 'team_member',
        'expense_date', 'due_date', 'payment_date'
    ]
    list_filter = ['type', 'status', 'category', 'expense_date']
    search_fields = ['title', 'description', 'team_member__user__first_name', 'team_member__user__last_name']
    date_hierarchy = 'expense_date'
    ordering = ['-created_at']
    readonly_fields = ['net_amount']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'description', 'type', 'category', 'status')
        }),
        ('التفاصيل المالية', {
            'fields': ('amount', 'tax_amount', 'net_amount')
        }),
        ('العلاقات', {
            'fields': ('team_member', 'project', 'approved_by')
        }),
        ('التواريخ', {
            'fields': ('expense_date', 'due_date', 'payment_date')
        }),
    )


@admin.register(CashFlowProjection)
class CashFlowProjectionAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'period_type', 'period_start', 'period_end',
        'projected_revenue', 'actual_revenue', 'projected_expenses', 'actual_expenses'
    ]
    list_filter = ['period_type', 'period_start']
    search_fields = ['title']
    date_hierarchy = 'period_start'
    ordering = ['-period_start']
    readonly_fields = ['projected_profit', 'actual_profit', 'revenue_variance', 'expense_variance']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'period_type', 'period_start', 'period_end')
        }),
        ('التوقعات', {
            'fields': ('projected_revenue', 'projected_expenses', 'projected_profit')
        }),
        ('النتائج الفعلية', {
            'fields': ('actual_revenue', 'actual_expenses', 'actual_profit')
        }),
        ('التحليل', {
            'fields': ('revenue_variance', 'expense_variance')
        }),
    )


class BudgetAllocationInline(admin.TabularInline):
    model = BudgetAllocation
    extra = 1
    readonly_fields = ['remaining_amount', 'utilization_percentage']


@admin.register(Budget)
class BudgetAdmin(SimpleHistoryAdmin):
    list_display = [
        'name', 'status', 'start_date', 'end_date',
        'total_budget', 'spent_amount', 'remaining_amount', 'utilization_percentage'
    ]
    list_filter = ['status', 'start_date']
    search_fields = ['name', 'description']
    date_hierarchy = 'start_date'
    ordering = ['-created_at']
    readonly_fields = ['remaining_amount', 'utilization_percentage', 'is_over_budget']
    inlines = [BudgetAllocationInline]
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'description', 'status')
        }),
        ('الفترة الزمنية', {
            'fields': ('start_date', 'end_date')
        }),
        ('الميزانية', {
            'fields': ('total_budget', 'allocated_amount', 'spent_amount', 'remaining_amount')
        }),
        ('التحليل', {
            'fields': ('utilization_percentage', 'is_over_budget')
        }),
    )


@admin.register(FinancialKPI)
class FinancialKPIAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'type', 'current_value', 'target_value',
        'achievement_percentage', 'status', 'period_start', 'period_end'
    ]
    list_filter = ['type', 'period_start']
    search_fields = ['name', 'description']
    date_hierarchy = 'period_start'
    ordering = ['-period_start']
    readonly_fields = ['achievement_percentage', 'growth_rate', 'status']
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'type', 'description')
        }),
        ('القيم', {
            'fields': ('target_value', 'current_value', 'previous_value')
        }),
        ('الفترة الزمنية', {
            'fields': ('period_start', 'period_end')
        }),
        ('التحليل', {
            'fields': ('achievement_percentage', 'growth_rate', 'status')
        }),
    )


# Register the models with custom admin classes
admin.site.register(BudgetAllocation)

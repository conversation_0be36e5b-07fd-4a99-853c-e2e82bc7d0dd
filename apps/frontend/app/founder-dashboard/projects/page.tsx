'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  FolderOpen,
  Search,
  Plus,
  Calendar,
  Users,
  DollarSign,
  Clock,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Loader2
} from 'lucide-react';
import { DEMO_PROJECTS } from '@/lib/demo-data';
import { formatRelativeTime } from '@mtbrmg/shared';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { AddProjectForm, type ProjectFormData } from '@/components/forms/add-project-form';
import { EditProjectForm } from '@/components/forms/edit-project-form';
import { DeleteProjectDialog } from '@/components/dialogs/delete-project-dialog';
import { ProjectDetailsDialog } from '@/components/dialogs/project-details-dialog';
import { projectsAPI, clientsAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export default function ProjectsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isAddProjectModalOpen, setIsAddProjectModalOpen] = useState(false);
  const [isEditProjectModalOpen, setIsEditProjectModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projects, setProjects] = useState(DEMO_PROJECTS);
  const [isLoading, setIsLoading] = useState(false);
  const [clients, setClients] = useState([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/login');
    }
  }, [isAuthenticated, router, mounted]);

  // Load projects and clients from API
  useEffect(() => {
    const loadData = async () => {
      if (!isAuthenticated || !mounted) return;

      setIsLoading(true);
      try {
        // Load projects and clients in parallel
        const [projectsResponse, clientsResponse] = await Promise.all([
          projectsAPI.getProjects(),
          clientsAPI.getClients()
        ]);

        setProjects(projectsResponse.results || projectsResponse);
        setClients(clientsResponse.results || clientsResponse);
      } catch (error) {
        console.error('Error loading data:', error);
        // Fallback to demo data if API fails
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isAuthenticated, mounted]);

  const handleAddProject = () => {
    router.push('/founder-dashboard/projects/new');
  };

  const handleSubmitProject = async (projectData: ProjectFormData) => {
    setIsLoading(true);

    try {
      // Check authentication first
      if (!isAuthenticated || !user) {
        throw new Error('المستخدم غير مسجل الدخول');
      }

      // Transform frontend form data to backend API format
      const apiData = {
        name: projectData.name,
        description: projectData.description,
        type: projectData.type,
        status: projectData.status,
        priority: projectData.priority,
        client: parseInt(projectData.client_id), // Backend expects client (ForeignKey ID)
        start_date: projectData.start_date,
        end_date: projectData.end_date || null,
        deadline: projectData.deadline || null,
        budget: projectData.budget || null,
        domains: projectData.domains ? projectData.domains.split(',').map(d => d.trim()).filter(d => d) : [],
        repository_url: projectData.repository_url || '',
        staging_url: projectData.staging_url || '',
        production_url: projectData.production_url || '',
        assigned_team_ids: user?.id ? [parseInt(user.id)] : [], // Backend expects array of integers
        project_manager_id: user?.id ? parseInt(user.id) : null
      };

      console.log('Frontend form data:', projectData);
      console.log('Transformed API data:', apiData);
      console.log('Available clients:', clients);
      console.log('Current user:', user);
      console.log('Is authenticated:', isAuthenticated);

      // Make API call to create project
      console.log('Making API call to create project...');
      const createdProject = await projectsAPI.createProject(apiData);
      console.log('Project created successfully:', createdProject);

      // Add the new project to local state
      setProjects(prev => [createdProject, ...prev]);

      // Close modal
      setIsAddProjectModalOpen(false);

      // Show success message
      showToast.success('تم إضافة المشروع بنجاح! 🎉', 'تم إضافة المشروع الجديد بنجاح');

    } catch (error: any) {
      console.error('Error adding project:', error);

      // Extract error message from API response
      let errorMessage = 'حدث خطأ أثناء إضافة المشروع. يرجى المحاولة مرة أخرى.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else {
          // Handle field-specific errors
          const fieldErrors = Object.entries(error.response.data)
            .map(([field, errors]: [string, any]) => {
              if (Array.isArray(errors)) {
                return `${field}: ${errors.join(', ')}`;
              }
              return `${field}: ${errors}`;
            })
            .join('\n');

          if (fieldErrors) {
            errorMessage = `خطأ في البيانات:\n${fieldErrors}`;
          }
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      showToast.error('فشل في إضافة المشروع', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Project action handlers
  const handleViewProject = (project) => {
    setSelectedProject(project);
    setIsDetailsDialogOpen(true);
  };

  const handleEditProject = (project) => {
    setSelectedProject(project);
    setIsEditProjectModalOpen(true);
  };

  const handleDeleteProject = (project) => {
    setSelectedProject(project);
    setIsDeleteDialogOpen(true);
  };

  const handleSubmitEditProject = async (projectData) => {
    try {
      console.log('Updating project:', selectedProject.id, projectData);
      const updatedProject = await projectsAPI.updateProject(selectedProject.id, projectData);
      console.log('Project updated successfully:', updatedProject);

      // Update local state
      setProjects(prev => prev.map(project =>
        project.id === selectedProject.id ? updatedProject : project
      ));

      // Close modal
      setIsEditProjectModalOpen(false);
      setSelectedProject(null);

      // Show success message
      showToast.success('تم تحديث المشروع بنجاح! ✅', 'تم حفظ التغييرات بنجاح');

    } catch (error) {
      console.error('Error updating project:', error);
      let errorMessage = 'حدث خطأ أثناء تحديث المشروع. يرجى المحاولة مرة أخرى.';

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (error.response.data.detail) {
          errorMessage = error.response.data.detail;
        }
      }

      showToast.error('فشل في تحديث المشروع', errorMessage);
    }
  };

  const handleConfirmDeleteProject = async (projectId) => {
    try {
      console.log('Deleting project:', projectId);
      await projectsAPI.deleteProject(projectId);
      console.log('Project deleted successfully');

      // Remove from local state
      setProjects(prev => prev.filter(project => project.id !== projectId));

      // Close dialog
      setIsDeleteDialogOpen(false);
      setSelectedProject(null);

      // Show success message
      showToast.success('تم حذف المشروع بنجاح! 🗑️', 'تم إزالة المشروع نهائياً');

    } catch (error) {
      console.error('Error deleting project:', error);
      let errorMessage = 'حدث خطأ أثناء حذف المشروع. يرجى المحاولة مرة أخرى.';

      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      showToast.error('فشل في حذف المشروع', errorMessage);
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mt-2">جاري تحميل إدارة المشاريع...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى إدارة المشاريع</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  // Filter projects based on search and status
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planning':
        return <Badge className="bg-blue-100 text-blue-800">التخطيط</Badge>;
      case 'development':
        return <Badge className="bg-yellow-100 text-yellow-800">التطوير</Badge>;
      case 'testing':
        return <Badge className="bg-orange-100 text-orange-800">الاختبار</Badge>;
      case 'deployment':
        return <Badge className="bg-purple-100 text-purple-800">النشر</Badge>;
      case 'maintenance':
        return <Badge className="bg-gray-100 text-gray-800">الصيانة</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">مكتمل</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">ملغي</Badge>;
      // Legacy status support
      case 'active':
        return <Badge className="bg-green-100 text-green-800">نشط</Badge>;
      case 'on_hold':
        return <Badge className="bg-yellow-100 text-yellow-800">معلق</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">عالي</Badge>;
      case 'medium':
        return <Badge variant="secondary">متوسط</Badge>;
      case 'low':
        return <Badge variant="outline">منخفض</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <FolderOpen className="h-8 w-8 text-purple-600" />
              إدارة المشاريع
            </h1>
            <p className="text-gray-600 mt-1">
              إدارة شاملة لجميع مشاريع الوكالة الرقمية
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              className="bg-purple-600 hover:bg-purple-700"
              onClick={handleAddProject}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 ml-2" />
              )}
              إضافة مشروع جديد
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المشاريع..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('all')}
                >
                  الكل ({DEMO_PROJECTS.length})
                </Button>
                <Button
                  variant={statusFilter === 'active' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('active')}
                >
                  نشط ({DEMO_PROJECTS.filter(p => p.status === 'active').length})
                </Button>
                <Button
                  variant={statusFilter === 'completed' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('completed')}
                >
                  مكتمل ({DEMO_PROJECTS.filter(p => p.status === 'completed').length})
                </Button>
                <Button
                  variant={statusFilter === 'on_hold' ? 'default' : 'outline'}
                  onClick={() => setStatusFilter('on_hold')}
                >
                  معلق ({DEMO_PROJECTS.filter(p => p.status === 'on_hold').length})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loading State */}
        {isLoading && (
          <Card className="text-center py-12">
            <CardContent>
              <Loader2 className="h-12 w-12 text-purple-600 mx-auto mb-4 animate-spin" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">جاري تحميل المشاريع...</h3>
              <p className="text-gray-600">يرجى الانتظار</p>
            </CardContent>
          </Card>
        )}

        {/* Projects Grid */}
        {!isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(project.status)}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewProject(project)}>
                          <Eye className="h-4 w-4 ml-2" />
                          عرض التفاصيل
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditProject(project)}>
                          <Edit className="h-4 w-4 ml-2" />
                          تعديل
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteProject(project)}
                        >
                          <Trash2 className="h-4 w-4 ml-2" />
                          حذف
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                <CardDescription className="text-sm text-gray-600">
                  {project.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">الأولوية:</span>
                    {getPriorityBadge(project.priority)}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <DollarSign className="h-4 w-4" />
                    <span>الميزانية: {project.budget ? project.budget.toLocaleString() : 'غير محدد'} ر.س</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Users className="h-4 w-4" />
                    <span>العميل: {project.client_name || 'غير محدد'}</span>
                  </div>
                  {project.start_date && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      <span>بدأ {formatRelativeTime(project.start_date)}</span>
                    </div>
                  )}
                  {project.end_date && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>ينتهي {formatRelativeTime(project.end_date)}</span>
                    </div>
                  )}
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">التقدم:</span>
                      <span className="font-medium">{project.progress || 0}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-purple-600 h-2 rounded-full"
                        style={{ width: `${project.progress || 0}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredProjects.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'لا توجد مشاريع تطابق معايير البحث المحددة'
                  : 'لم يتم إضافة أي مشاريع بعد'
                }
              </p>
              {(!searchTerm && statusFilter === 'all') && (
                <Button
                  className="bg-purple-600 hover:bg-purple-700"
                  onClick={handleAddProject}
                  disabled={isLoading}
                >
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة أول مشروع
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Modals and Dialogs */}
      <AddProjectForm
        isOpen={isAddProjectModalOpen}
        onClose={() => setIsAddProjectModalOpen(false)}
        onSubmit={handleSubmitProject}
        clients={clients}
      />

      <EditProjectForm
        isOpen={isEditProjectModalOpen}
        onClose={() => {
          setIsEditProjectModalOpen(false);
          setSelectedProject(null);
        }}
        onSubmit={handleSubmitEditProject}
        project={selectedProject}
        clients={clients}
      />

      <DeleteProjectDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => {
          setIsDeleteDialogOpen(false);
          setSelectedProject(null);
        }}
        onConfirm={handleConfirmDeleteProject}
        project={selectedProject}
      />

      <ProjectDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={() => {
          setIsDetailsDialogOpen(false);
          setSelectedProject(null);
        }}
        onEdit={() => {
          setIsDetailsDialogOpen(false);
          handleEditProject(selectedProject);
        }}
        onDelete={() => {
          setIsDetailsDialogOpen(false);
          handleDeleteProject(selectedProject);
        }}
        project={selectedProject}
      />
    </UnifiedLayout>
  );
}

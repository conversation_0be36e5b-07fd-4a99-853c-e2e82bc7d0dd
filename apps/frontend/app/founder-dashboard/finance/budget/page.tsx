'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  PiggyBank,
  Target,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  BarChart3,
  Loader2,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showToast } from '@/lib/toast';

export default function BudgetPlanningPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [statusFilter, setStatusFilter] = useState('all');
  const [budgets, setBudgets] = useState([]);
  const [budgetSummary, setBudgetSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchBudgetData();
    }
  }, [mounted, isAuthenticated, selectedYear, statusFilter]);

  const fetchBudgetData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build filter parameters
      const params = {};
      if (selectedYear !== 'all') params.year = selectedYear;
      if (statusFilter !== 'all') params.status = statusFilter;

      // Fetch budgets
      const budgetResponse = await financeAPI.getBudgets(params);
      setBudgets(budgetResponse.results || budgetResponse);

      // Fetch budget summary
      const summaryResponse = await financeAPI.getBudgetSummary();
      setBudgetSummary(summaryResponse);

    } catch (err) {
      console.error('Error fetching budget data:', err);
      setError('حدث خطأ في تحميل بيانات الميزانية');
      showToast.error('فشل في تحميل بيانات الميزانية');
    } finally {
      setLoading(false);
    }
  };

  const handleAddBudget = () => {
    router.push('/founder-dashboard/finance/budget/add');
  };

  const handleEditBudget = (budget) => {
    router.push(`/founder-dashboard/finance/budget/edit/${budget.id}`);
  };

  const handleDeleteBudget = async (budget) => {
    if (confirm('هل أنت متأكد من حذف هذه الميزانية؟')) {
      try {
        await financeAPI.deleteBudget(budget.id);
        showToast.success('تم حذف الميزانية بنجاح');
        fetchBudgetData();
      } catch (err) {
        console.error('Error deleting budget:', err);
        showToast.error('فشل في حذف الميزانية');
      }
    }
  };

  const handleActivateBudget = async (budget) => {
    try {
      await financeAPI.activateBudget(budget.id);
      showToast.success('تم تفعيل الميزانية بنجاح');
      fetchBudgetData();
    } catch (err) {
      console.error('Error activating budget:', err);
      showToast.error('فشل في تفعيل الميزانية');
    }
  };

  const handleViewBudget = (budget) => {
    router.push(`/founder-dashboard/finance/budget/view/${budget.id}`);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="text-gray-600 border-gray-600">مسودة</Badge>;
      case 'active':
        return <Badge variant="outline" className="text-green-600 border-green-600">نشط</Badge>;
      case 'completed':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">مكتمل</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getUtilizationColor = (percentage) => {
    const numPercentage = Number(percentage) || 0;
    if (numPercentage >= 90) return 'text-red-600';
    if (numPercentage >= 75) return 'text-orange-600';
    if (numPercentage >= 50) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUtilizationIcon = (percentage) => {
    const numPercentage = Number(percentage) || 0;
    if (numPercentage >= 90) return <AlertTriangle className="h-4 w-4 text-red-600" />;
    if (numPercentage >= 75) return <TrendingUp className="h-4 w-4 text-orange-600" />;
    return <CheckCircle className="h-4 w-4 text-green-600" />;
  };

  const safeUtilizationPercentage = (percentage) => {
    const numPercentage = Number(percentage);
    return isNaN(numPercentage) ? 0 : numPercentage;
  };

  const getCurrentYear = () => new Date().getFullYear();
  const getYearOptions = () => {
    const currentYear = getCurrentYear();
    return Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل تخطيط الميزانية...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى تخطيط الميزانية</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل بيانات الميزانية...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <PiggyBank className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchBudgetData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <PiggyBank className="h-8 w-8 text-orange-600" />
              تخطيط الميزانية
            </h1>
            <p className="text-gray-600 mt-1">
              إنشاء ومراقبة الميزانيات والتحكم في الإنفاق
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={handleAddBudget} className="bg-orange-600 hover:bg-orange-700">
              <Plus className="h-4 w-4 ml-2" />
              إنشاء ميزانية جديدة
            </Button>
          </div>
        </div>

        {/* Budget Summary Cards */}
        {budgetSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الميزانية</CardTitle>
                <DollarSign className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(budgetSummary.total_budget)}
                </div>
                <p className="text-xs text-gray-600">للسنة الحالية</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المبلغ المستخدم</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(budgetSummary.total_spent)}
                </div>
                <p className="text-xs text-gray-600">من الميزانية</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">المبلغ المتبقي</CardTitle>
                <Target className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(budgetSummary.total_remaining)}
                </div>
                <p className="text-xs text-gray-600">متاح للإنفاق</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">نسبة الاستخدام</CardTitle>
                <BarChart3 className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {safeUtilizationPercentage(budgetSummary.utilization_percentage).toFixed(1)}%
                </div>
                <p className="text-xs text-gray-600">من الميزانية</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  السنة المالية
                </label>
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر السنة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع السنوات</SelectItem>
                    {getYearOptions().map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  حالة الميزانية
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="draft">مسودة</SelectItem>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="completed">مكتمل</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Budget List */}
        <div className="grid grid-cols-1 gap-6">
          {budgets.map((budget) => (
            <Card key={budget.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <h3 className="text-xl font-semibold">{budget.name}</h3>
                      {getStatusBadge(budget.status)}
                      <Badge variant="secondary">
                        {budget.year}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{budget.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">إجمالي الميزانية</span>
                          <span className="text-lg font-bold text-orange-600">
                            {formatCurrency(budget.total_budget)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600">المبلغ المستخدم</span>
                          <span className="font-medium text-red-600">
                            {formatCurrency(budget.total_spent)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">المبلغ المتبقي</span>
                          <span className="font-medium text-green-600">
                            {formatCurrency(budget.total_remaining)}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">نسبة الاستخدام</span>
                          <div className="flex items-center gap-2">
                            {getUtilizationIcon(budget.utilization_percentage)}
                            <span className={`font-bold ${getUtilizationColor(budget.utilization_percentage)}`}>
                              {safeUtilizationPercentage(budget.utilization_percentage).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <Progress
                          value={safeUtilizationPercentage(budget.utilization_percentage)}
                          className="mb-2"
                        />
                        <div className="text-xs text-gray-500">
                          {safeUtilizationPercentage(budget.utilization_percentage) >= 90 && 'تحذير: تجاوز 90% من الميزانية'}
                          {safeUtilizationPercentage(budget.utilization_percentage) >= 75 && safeUtilizationPercentage(budget.utilization_percentage) < 90 && 'تنبيه: تجاوز 75% من الميزانية'}
                          {safeUtilizationPercentage(budget.utilization_percentage) < 75 && 'ضمن الحدود المقبولة'}
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">الفترة</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          <div className="flex items-center gap-1 mb-1">
                            <Calendar className="h-3 w-3" />
                            من: {formatDate(budget.start_date)}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            إلى: {formatDate(budget.end_date)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewBudget(budget)}>
                        <Eye className="h-4 w-4 ml-2" />
                        عرض التفاصيل
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditBudget(budget)}>
                        <Edit className="h-4 w-4 ml-2" />
                        تعديل
                      </DropdownMenuItem>
                      {budget.status === 'draft' && (
                        <DropdownMenuItem 
                          className="text-green-600"
                          onClick={() => handleActivateBudget(budget)}
                        >
                          <CheckCircle className="h-4 w-4 ml-2" />
                          تفعيل
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteBudget(budget)}
                      >
                        <Trash2 className="h-4 w-4 ml-2" />
                        حذف
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {budgets.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <PiggyBank className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد ميزانيات</h3>
              <p className="text-gray-600 mb-4">
                {selectedYear !== 'all' || statusFilter !== 'all'
                  ? 'لا توجد ميزانيات تطابق معايير البحث المحددة'
                  : 'لم يتم إنشاء أي ميزانيات بعد'
                }
              </p>
              {selectedYear === 'all' && statusFilter === 'all' && (
                <Button onClick={handleAddBudget} className="bg-orange-600 hover:bg-orange-700">
                  <Plus className="h-4 w-4 ml-2" />
                  إنشاء أول ميزانية
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </UnifiedLayout>
  );
}

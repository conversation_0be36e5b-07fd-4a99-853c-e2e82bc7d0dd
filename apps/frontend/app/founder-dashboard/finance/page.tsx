'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Receipt,
  FileText,
  PiggyBank,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  BarChart3,
  Loader2
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import { showToast } from '@/lib/toast';

export default function FinanceDashboardPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [financialOverview, setFinancialOverview] = useState(null);
  const [departmentAnalysis, setDepartmentAnalysis] = useState([]);
  const [financialTrends, setFinancialTrends] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchFinancialData();
    }
  }, [mounted, isAuthenticated]);

  const fetchFinancialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch financial overview
      const overviewResponse = await financeAPI.getFinancialOverview();
      setFinancialOverview(overviewResponse);

      // Fetch department analysis
      const departmentResponse = await financeAPI.getDepartmentAnalysis();
      setDepartmentAnalysis(departmentResponse);

      // Fetch financial trends (last 6 months)
      const trendsResponse = await financeAPI.getFinancialTrends(6);
      setFinancialTrends(trendsResponse);

    } catch (err) {
      console.error('Error fetching financial data:', err);
      setError('حدث خطأ في تحميل البيانات المالية');
      showToast.error('فشل في تحميل البيانات المالية');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'average': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل الشؤون المالية...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الشؤون المالية</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل البيانات المالية...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <DollarSign className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchFinancialData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <DollarSign className="h-8 w-8 text-green-600" />
              الشؤون المالية والمحاسبة
            </h1>
            <p className="text-gray-600 mt-1">
              نظرة شاملة على الوضع المالي للشركة وأدوات اتخاذ القرارات المالية
            </p>
          </div>
        </div>

        {/* Financial Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(financialOverview?.revenue?.total || 0)}
              </div>
              <p className="text-xs text-gray-600">
                الشهر الحالي: {formatCurrency(financialOverview?.revenue?.monthly || 0)}
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(financialOverview?.expenses?.total || 0)}
              </div>
              <p className="text-xs text-gray-600">
                الشهر الحالي: {formatCurrency(financialOverview?.expenses?.monthly || 0)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">صافي الربح</CardTitle>
              <Target className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(financialOverview?.profit?.total || 0)}
              </div>
              <p className="text-xs text-gray-600">
                هامش الربح: {financialOverview?.margins?.total?.toFixed(1) || 0}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">العناصر المعلقة</CardTitle>
              <Clock className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>فواتير معلقة:</span>
                  <span className="font-medium">{financialOverview?.pending?.invoices || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>فواتير متأخرة:</span>
                  <span className="font-medium text-red-600">{financialOverview?.pending?.overdue_invoices || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مصروفات معلقة:</span>
                  <span className="font-medium">{financialOverview?.pending?.expenses || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
          <Button 
            onClick={() => router.push('/founder-dashboard/finance/revenue')}
            className="bg-green-600 hover:bg-green-700 h-16 flex flex-col items-center justify-center"
          >
            <TrendingUp className="h-5 w-5 mb-1" />
            إدارة الإيرادات
          </Button>
          
          <Button 
            onClick={() => router.push('/founder-dashboard/finance/expenses')}
            className="bg-red-600 hover:bg-red-700 h-16 flex flex-col items-center justify-center"
          >
            <TrendingDown className="h-5 w-5 mb-1" />
            تتبع المصروفات
          </Button>
          
          <Button 
            onClick={() => router.push('/founder-dashboard/finance/cash-flow')}
            className="bg-blue-600 hover:bg-blue-700 h-16 flex flex-col items-center justify-center"
          >
            <Receipt className="h-5 w-5 mb-1" />
            التدفق النقدي
          </Button>
          
          <Button 
            onClick={() => router.push('/founder-dashboard/finance/reports')}
            className="bg-purple-600 hover:bg-purple-700 h-16 flex flex-col items-center justify-center"
          >
            <FileText className="h-5 w-5 mb-1" />
            التقارير المالية
          </Button>
          
          <Button 
            onClick={() => router.push('/founder-dashboard/finance/budget')}
            className="bg-orange-600 hover:bg-orange-700 h-16 flex flex-col items-center justify-center"
          >
            <PiggyBank className="h-5 w-5 mb-1" />
            تخطيط الميزانية
          </Button>
        </div>

        {/* Department Financial Analysis */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              التحليل المالي حسب الأقسام
            </CardTitle>
            <CardDescription>
              أداء الأقسام المختلفة من الناحية المالية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {departmentAnalysis.map((dept, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">
                    {dept.department === 'sales' && 'فريق المبيعات'}
                    {dept.department === 'development' && 'فريق المطورين'}
                    {dept.department === 'design' && 'فريق المصممين'}
                    {dept.department === 'media_buying' && 'فريق مشتري الإعلانات'}
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>الإيرادات:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(dept.total_revenue)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>المصروفات:</span>
                      <span className="font-medium text-red-600">
                        {formatCurrency(dept.total_expenses)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>هامش الربح:</span>
                      <span className={`font-medium ${dept.profit_margin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {dept.profit_margin.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>المشاريع:</span>
                      <span className="font-medium">{dept.project_count}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Financial Trends Chart Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              الاتجاهات المالية (آخر 6 أشهر)
            </CardTitle>
            <CardDescription>
              تطور الإيرادات والمصروفات والأرباح خلال الفترة الماضية
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">مخطط الاتجاهات المالية</p>
                <p className="text-sm text-gray-400">سيتم إضافة مخطط تفاعلي هنا</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Download,
  Calendar,
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  FileBarChart,
  Loader2,
  Filter,
  Eye,
  Share2
} from 'lucide-react';
import { UnifiedLayout } from '@/components/layout';
import { useAuthStore } from '@/lib/stores/auth-store';
import { financeAPI } from '@/lib/api';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { showToast } from '@/lib/toast';

export default function FinancialReportsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [mounted, setMounted] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('current_month');
  const [selectedReportType, setSelectedReportType] = useState('all');
  const [reports, setReports] = useState([]);
  const [reportSummary, setReportSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && isAuthenticated) {
      fetchReportsData();
    }
  }, [mounted, isAuthenticated, selectedPeriod, selectedReportType]);

  const fetchReportsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build filter parameters
      const params = {};
      if (selectedPeriod !== 'all') params.period = selectedPeriod;
      if (selectedReportType !== 'all') params.type = selectedReportType;

      // Fetch reports
      const reportsResponse = await financeAPI.getFinancialReports(params);
      setReports(reportsResponse.results || reportsResponse);

      // Fetch report summary
      const summaryResponse = await financeAPI.getReportsSummary();
      setReportSummary(summaryResponse);

    } catch (err) {
      console.error('Error fetching reports data:', err);
      setError('حدث خطأ في تحميل التقارير المالية');
      showToast.error('فشل في تحميل التقارير المالية');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = (reportType) => {
    router.push(`/founder-dashboard/finance/reports/generate?type=${reportType}`);
  };

  const handleViewReport = (report) => {
    router.push(`/founder-dashboard/finance/reports/view/${report.id}`);
  };

  const handleDownloadReport = async (report) => {
    try {
      await financeAPI.downloadReport(report.id);
      showToast.success('تم تحميل التقرير بنجاح');
    } catch (err) {
      console.error('Error downloading report:', err);
      showToast.error('فشل في تحميل التقرير');
    }
  };

  const handleShareReport = (report) => {
    // TODO: Implement share functionality
    showToast.info('سيتم إضافة وظيفة المشاركة قريباً');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  const getReportTypeDisplay = (type) => {
    const types = {
      'profit_loss': 'الأرباح والخسائر',
      'cash_flow': 'التدفق النقدي',
      'balance_sheet': 'الميزانية العمومية',
      'budget_analysis': 'تحليل الميزانية',
      'department_performance': 'أداء الأقسام',
      'client_profitability': 'ربحية العملاء',
      'project_financial': 'التقرير المالي للمشاريع',
      'expense_analysis': 'تحليل المصروفات',
      'revenue_analysis': 'تحليل الإيرادات'
    };
    return types[type] || type;
  };

  const getReportIcon = (type) => {
    switch (type) {
      case 'profit_loss':
        return <TrendingUp className="h-5 w-5 text-green-600" />;
      case 'cash_flow':
        return <DollarSign className="h-5 w-5 text-blue-600" />;
      case 'balance_sheet':
        return <BarChart3 className="h-5 w-5 text-purple-600" />;
      case 'budget_analysis':
        return <PieChart className="h-5 w-5 text-orange-600" />;
      case 'department_performance':
        return <FileBarChart className="h-5 w-5 text-indigo-600" />;
      default:
        return <FileText className="h-5 w-5 text-gray-600" />;
    }
  };

  const reportTypes = [
    { value: 'profit_loss', label: 'تقرير الأرباح والخسائر', description: 'تحليل الإيرادات والمصروفات وصافي الربح' },
    { value: 'cash_flow', label: 'تقرير التدفق النقدي', description: 'تتبع التدفقات النقدية الداخلة والخارجة' },
    { value: 'balance_sheet', label: 'الميزانية العمومية', description: 'الأصول والخصوم وحقوق الملكية' },
    { value: 'budget_analysis', label: 'تحليل الميزانية', description: 'مقارنة الميزانية المخططة مع الفعلية' },
    { value: 'department_performance', label: 'أداء الأقسام', description: 'التحليل المالي لكل قسم' },
    { value: 'client_profitability', label: 'ربحية العملاء', description: 'تحليل الربحية لكل عميل' },
    { value: 'project_financial', label: 'التقرير المالي للمشاريع', description: 'التكاليف والإيرادات لكل مشروع' },
    { value: 'expense_analysis', label: 'تحليل المصروفات', description: 'تفصيل المصروفات حسب الفئات' },
    { value: 'revenue_analysis', label: 'تحليل الإيرادات', description: 'تفصيل الإيرادات حسب المصادر' }
  ];

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل التقارير المالية...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <h1 className="text-2xl font-bold text-purple-600 mb-4">MTBRMG ERP</h1>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى التقارير المالية</p>
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <UnifiedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
            <p className="text-gray-600">جاري تحميل التقارير المالية...</p>
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  if (error) {
    return (
      <UnifiedLayout>
        <div className="max-w-7xl mx-auto">
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-red-500 mb-4">
                <FileText className="h-12 w-12 mx-auto mb-4" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchReportsData} className="bg-purple-600 hover:bg-purple-700">
                إعادة المحاولة
              </Button>
            </CardContent>
          </Card>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <FileText className="h-8 w-8 text-purple-600" />
              التقارير المالية
            </h1>
            <p className="text-gray-600 mt-1">
              إنشاء وعرض التقارير المالية التفصيلية والتحليلية
            </p>
          </div>
        </div>

        {/* Report Summary Cards */}
        {reportSummary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">إجمالي التقارير</CardTitle>
                <FileText className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  {reportSummary.total_reports}
                </div>
                <p className="text-xs text-gray-600">تقرير متاح</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">تقارير هذا الشهر</CardTitle>
                <Calendar className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {reportSummary.monthly_reports}
                </div>
                <p className="text-xs text-gray-600">تقرير جديد</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">التقارير المجدولة</CardTitle>
                <BarChart3 className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {reportSummary.scheduled_reports}
                </div>
                <p className="text-xs text-gray-600">تقرير تلقائي</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">التحميلات</CardTitle>
                <Download className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {reportSummary.total_downloads}
                </div>
                <p className="text-xs text-gray-600">مرة تحميل</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Quick Report Generation */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              إنشاء تقرير سريع
            </CardTitle>
            <CardDescription>
              اختر نوع التقرير المطلوب لإنشائه فوراً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {reportTypes.map((reportType) => (
                <Card key={reportType.value} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      {getReportIcon(reportType.value)}
                      <div className="flex-1">
                        <h4 className="font-medium mb-1">{reportType.label}</h4>
                        <p className="text-sm text-gray-600 mb-3">{reportType.description}</p>
                        <Button 
                          size="sm" 
                          onClick={() => handleGenerateReport(reportType.value)}
                          className="w-full"
                        >
                          إنشاء التقرير
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفترة الزمنية
                </label>
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفترة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفترات</SelectItem>
                    <SelectItem value="current_month">الشهر الحالي</SelectItem>
                    <SelectItem value="last_month">الشهر الماضي</SelectItem>
                    <SelectItem value="current_quarter">الربع الحالي</SelectItem>
                    <SelectItem value="last_quarter">الربع الماضي</SelectItem>
                    <SelectItem value="current_year">السنة الحالية</SelectItem>
                    <SelectItem value="last_year">السنة الماضية</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع التقرير
                </label>
                <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    {reportTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reports List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              التقارير المتاحة
            </CardTitle>
            <CardDescription>
              جميع التقارير المالية المُنشأة والمحفوظة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reports.map((report) => (
                <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-4">
                    {getReportIcon(report.type)}
                    <div>
                      <h4 className="font-medium">{report.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>{getReportTypeDisplay(report.type)}</span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatDate(report.created_at)}
                        </span>
                        <span>•</span>
                        <span>{report.period_display}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewReport(report)}
                    >
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownloadReport(report)}
                    >
                      <Download className="h-4 w-4 ml-1" />
                      تحميل
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleShareReport(report)}
                    >
                      <Share2 className="h-4 w-4 ml-1" />
                      مشاركة
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {reports.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تقارير</h3>
                <p className="text-gray-600 mb-4">
                  {selectedPeriod !== 'all' || selectedReportType !== 'all'
                    ? 'لا توجد تقارير تطابق معايير البحث المحددة'
                    : 'لم يتم إنشاء أي تقارير مالية بعد'
                  }
                </p>
                <Button onClick={() => handleGenerateReport('profit_loss')} className="bg-purple-600 hover:bg-purple-700">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  إنشاء أول تقرير
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </UnifiedLayout>
  );
}

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { projectsAPI } from '@/lib/api';
import { useProjectStore } from '@/lib/stores/app-store';
import { toast } from 'sonner';
import type { Project } from '@mtbrmg/shared';

// Query keys
export const projectKeys = {
  all: ['projects'] as const,
  lists: () => [...projectKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...projectKeys.lists(), filters] as const,
  details: () => [...projectKeys.all, 'detail'] as const,
  detail: (id: string) => [...projectKeys.details(), id] as const,
  stats: () => [...projectKeys.all, 'stats'] as const,
  byClient: (clientId: string) => [...projectKeys.all, 'client', clientId] as const,
};

// Hooks for projects data
export function useProjects(params?: Record<string, any>) {
  const { setProjects, setLoading, setError } = useProjectStore();

  return useQuery({
    queryKey: projectKeys.list(params || {}),
    queryFn: () => projectsAPI.getProjects(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    onSuccess: (data) => {
      setProjects(data.results || data);
      setLoading(false);
      setError(null);
    },
    onError: (error: any) => {
      setError(error.message || 'فشل في جلب بيانات المشاريع');
      setLoading(false);
    },
  });
}

export function useProject(id: string) {
  return useQuery({
    queryKey: projectKeys.detail(id),
    queryFn: () => projectsAPI.getProject(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

export function useProjectsByClient(clientId: string) {
  return useQuery({
    queryKey: projectKeys.byClient(clientId),
    queryFn: () => projectsAPI.getProjects({ client: clientId }),
    enabled: !!clientId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

export function useProjectStats() {
  return useQuery({
    queryKey: projectKeys.stats(),
    queryFn: () => projectsAPI.getProjects({ stats: true }),
    staleTime: 2 * 60 * 1000, // 2 minutes for stats
    gcTime: 5 * 60 * 1000,
  });
}

// Mutations for project operations
export function useCreateProject() {
  const queryClient = useQueryClient();
  const { addProject } = useProjectStore();

  return useMutation({
    mutationFn: projectsAPI.createProject,
    onSuccess: (newProject) => {
      // Update the store
      addProject(newProject);
      
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: projectKeys.all });
      
      toast.success('تم إنشاء المشروع بنجاح');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'فشل في إنشاء المشروع');
    },
  });
}

export function useUpdateProject() {
  const queryClient = useQueryClient();
  const { updateProject } = useProjectStore();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Project> }) =>
      projectsAPI.updateProject(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: projectKeys.detail(id) });

      // Snapshot the previous value
      const previousProject = queryClient.getQueryData(projectKeys.detail(id));

      // Optimistically update the cache
      queryClient.setQueryData(projectKeys.detail(id), (old: any) => ({
        ...old,
        ...data,
      }));

      // Update the store optimistically
      updateProject(id, data);

      return { previousProject, id };
    },
    onError: (error: any, { id }, context) => {
      // Rollback on error
      if (context?.previousProject) {
        queryClient.setQueryData(projectKeys.detail(id), context.previousProject);
      }
      toast.error(error.response?.data?.message || 'فشل في تحديث المشروع');
    },
    onSuccess: (updatedProject, { id }) => {
      // Update the store with the server response
      updateProject(id, updatedProject);
      toast.success('تم تحديث المشروع بنجاح');
    },
    onSettled: (_, __, { id }) => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: projectKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
    },
  });
}

export function useDeleteProject() {
  const queryClient = useQueryClient();
  const { removeProject } = useProjectStore();

  return useMutation({
    mutationFn: projectsAPI.deleteProject,
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: projectKeys.all });

      // Optimistically remove from store
      removeProject(id);

      return { id };
    },
    onError: (error: any, id) => {
      toast.error(error.response?.data?.message || 'فشل في حذف المشروع');
    },
    onSuccess: () => {
      toast.success('تم حذف المشروع بنجاح');
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: projectKeys.all });
    },
  });
}

// Combined hook for project management
export function useProjectManagement() {
  const queryClient = useQueryClient();
  
  const createProject = useCreateProject();
  const updateProject = useUpdateProject();
  const deleteProject = useDeleteProject();

  const invalidateProjects = () => {
    queryClient.invalidateQueries({ queryKey: projectKeys.all });
  };

  const prefetchProject = (id: string) => {
    queryClient.prefetchQuery({
      queryKey: projectKeys.detail(id),
      queryFn: () => projectsAPI.getProject(id),
      staleTime: 5 * 60 * 1000,
    });
  };

  const updateProjectProgress = useMutation({
    mutationFn: ({ id, progress }: { id: string; progress: number }) =>
      projectsAPI.updateProject(id, { progress }),
    onSuccess: (updatedProject) => {
      queryClient.setQueryData(
        projectKeys.detail(updatedProject.id),
        updatedProject
      );
      toast.success('تم تحديث تقدم المشروع');
    },
    onError: (error: any) => {
      toast.error('فشل في تحديث تقدم المشروع');
    },
  });

  return {
    createProject,
    updateProject,
    deleteProject,
    updateProjectProgress,
    invalidateProjects,
    prefetchProject,
  };
}

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginData, RegisterData } from '@mtbrmg/shared';
import { authAPI } from '../api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  getProfile: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (data: LoginData) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authAPI.login(data);
          
          // Store tokens
          localStorage.setItem('access_token', response.access);
          localStorage.setItem('refresh_token', response.refresh);
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          console.error('Login error:', error);
          let errorMessage = 'فشل في تسجيل الدخول';

          if (error.response?.data) {
            // Handle different error response formats
            if (error.response.data.detail) {
              errorMessage = error.response.data.detail;
            } else if (error.response.data.message) {
              errorMessage = error.response.data.message;
            } else if (error.response.data.non_field_errors) {
              errorMessage = error.response.data.non_field_errors[0];
            } else if (typeof error.response.data === 'string') {
              errorMessage = error.response.data;
            }
          }

          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await authAPI.register(data);
          
          // Store tokens
          localStorage.setItem('access_token', response.access);
          localStorage.setItem('refresh_token', response.refresh);
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'فشل في إنشاء الحساب';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          await authAPI.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API error:', error);
        } finally {
          // Clear local storage and state
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
          
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      getProfile: async () => {
        try {
          set({ isLoading: true, error: null });
          
          const user = await authAPI.getProfile();
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'فشل في جلب بيانات المستخدم';
          set({
            error: errorMessage,
            isLoading: false,
            isAuthenticated: false,
            user: null,
          });
          
          // Clear tokens if profile fetch fails
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      skipHydration: true,
    }
  )
);

// Helper function to check if user has specific role
export const useUserRole = () => {
  const user = useAuthStore((state) => state.user);
  
  return {
    isAdmin: () => user?.role === 'admin',
    isSalesManager: () => user?.role === 'sales_manager',
    isMediaBuyer: () => user?.role === 'media_buyer',
    isDeveloper: () => user?.role === 'developer',
    isDesigner: () => user?.role === 'designer',
    isWordPressDeveloper: () => user?.role === 'wordpress_developer',
    canManageClients: () => user?.role === 'admin' || user?.role === 'sales_manager',
    canManageProjects: () => 
      user?.role === 'admin' || 
      user?.role === 'sales_manager' || 
      user?.role === 'developer' || 
      user?.role === 'designer' || 
      user?.role === 'wordpress_developer',
  };
};

// Initialize auth state on app start
export const initializeAuth = async () => {
  try {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    const token = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');

    console.log('Initializing auth with tokens:', { hasToken: !!token, hasRefresh: !!refreshToken });

    if (token && refreshToken) {
      try {
        // First try to get profile with existing token
        await useAuthStore.getState().getProfile();
        console.log('Auth initialized with existing tokens');
      } catch (error) {
        console.error('Failed to initialize auth with existing tokens:', error);
        // Clear invalid tokens
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        useAuthStore.getState().logout();
      }
    } else {
      // No tokens found, ensure user is logged out
      console.log('No tokens found, setting logged out state');
      useAuthStore.setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  } catch (error) {
    console.error('Error during auth initialization:', error);
    // Fallback to logged out state
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
  }
};

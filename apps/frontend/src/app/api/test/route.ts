import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('API route called - testing backend connection');
    
    // Try to connect to the backend
    const backendUrl = 'http://backend:8000/api/health/';
    console.log('Attempting to connect to:', backendUrl);

    // Also try to resolve the hostname
    console.log('Testing hostname resolution...');
    try {
      const dns = require('dns');
      const util = require('util');
      const lookup = util.promisify(dns.lookup);
      const result = await lookup('backend');
      console.log('DNS lookup result for backend:', result);
    } catch (dnsError) {
      console.error('DNS lookup failed:', dnsError);
    }
    
    const response = await fetch(backendUrl);
    const data = await response.text();
    
    console.log('Backend response status:', response.status);
    console.log('Backend response data:', data);
    
    return NextResponse.json({
      success: true,
      backendStatus: response.status,
      backendData: data,
      message: 'Successfully connected to backend'
    });
  } catch (error) {
    console.error('Error connecting to backend:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to connect to backend'
    }, { status: 500 });
  }
}

'use client';

import { useEffect, useState } from 'react';
import { useAuthStore, initializeAuth } from '@/lib/stores/auth-store';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Hydrate the store on client side
    useAuthStore.persist.rehydrate();

    // Initialize auth state
    const initAuth = async () => {
      try {
        await initializeAuth();
        console.log('Auth initialized successfully');
      } catch (error) {
        console.error('Auth initialization failed:', error);
      } finally {
        setIsHydrated(true);
      }
    };

    initAuth();
  }, []); // Remove isAuthenticated dependency to avoid loops

  // Don't render children until hydration is complete
  if (!isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-purple-600">MTBRMG ERP</h1>
          <p className="text-gray-600 mt-2">جاري تحميل النظام...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

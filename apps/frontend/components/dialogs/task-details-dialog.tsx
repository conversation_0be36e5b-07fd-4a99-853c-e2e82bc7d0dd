'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  Flag,
  FolderOpen,
  Edit,
  Trash2,
  X,
  ExternalLink,
  TrendingUp
} from 'lucide-react';
import { formatRelativeTime } from '@mtbrmg/shared';

interface TaskDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  task: any;
}

export function TaskDetailsDialog({ 
  isOpen, 
  onClose, 
  onEdit, 
  onDelete, 
  task 
}: TaskDetailsDialogProps) {
  if (!task) return null;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'في الانتظار', className: 'bg-yellow-100 text-yellow-800' },
      in_progress: { label: 'قيد التنفيذ', className: 'bg-blue-100 text-blue-800' },
      review: { label: 'مراجعة', className: 'bg-purple-100 text-purple-800' },
      testing: { label: 'اختبار', className: 'bg-orange-100 text-orange-800' },
      completed: { label: 'مكتمل', className: 'bg-green-100 text-green-800' },
      cancelled: { label: 'ملغي', className: 'bg-red-100 text-red-800' }
    };
    
    const config = statusConfig[status] || statusConfig.pending;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: 'منخفض', className: 'bg-gray-100 text-gray-800' },
      medium: { label: 'متوسط', className: 'bg-blue-100 text-blue-800' },
      high: { label: 'عالي', className: 'bg-orange-100 text-orange-800' },
      urgent: { label: 'عاجل', className: 'bg-red-100 text-red-800' }
    };
    
    const config = priorityConfig[priority] || priorityConfig.medium;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      light: { label: 'خفيف (1-4 ساعات)', className: 'bg-green-100 text-green-800' },
      medium: { label: 'متوسط (4-8 ساعات)', className: 'bg-yellow-100 text-yellow-800' },
      extreme: { label: 'شاق (8+ ساعات)', className: 'bg-red-100 text-red-800' }
    };
    
    const config = categoryConfig[category] || categoryConfig.medium;
    return <Badge className={config.className}>{config.label}</Badge>;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <CheckSquare className="h-6 w-6 text-purple-600" />
            تفاصيل المهمة
          </DialogTitle>
          <DialogDescription>
            عرض تفاصيل المهمة والمعلومات المرتبطة بها
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{task.title}</CardTitle>
              <CardDescription>
                {task.description || 'لا يوجد وصف متاح'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Flag className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">الحالة:</span>
                  {getStatusBadge(task.status)}
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">الأولوية:</span>
                  {getPriorityBadge(task.priority)}
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">الفئة:</span>
                  {getCategoryBadge(task.category)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Time and Assignment */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  معلومات الوقت
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">الساعات المقدرة:</span>
                  <span className="font-medium">{task.estimated_hours} ساعة</span>
                </div>
                {task.actual_hours && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">الساعات الفعلية:</span>
                    <span className="font-medium">{task.actual_hours} ساعة</span>
                  </div>
                )}
                {task.start_date && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">تاريخ البداية:</span>
                    <span className="font-medium">
                      {new Date(task.start_date).toLocaleDateString('ar-EG')}
                    </span>
                  </div>
                )}
                {task.due_date && (
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">تاريخ الاستحقاق:</span>
                    <span className="font-medium">
                      {new Date(task.due_date).toLocaleDateString('ar-EG')}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="h-5 w-5" />
                  معلومات التكليف
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {task.project && (
                  <div className="flex items-center gap-2">
                    <FolderOpen className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">المشروع:</span>
                    <span className="font-medium">{task.project_name || task.project}</span>
                  </div>
                )}
                {task.assigned_to && (
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">مكلف إلى:</span>
                    <span className="font-medium">{task.assigned_to_name || task.assigned_to}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">منشئ المهمة:</span>
                  <span className="font-medium">{task.created_by_name || task.created_by}</span>
                </div>
                {task.created_at && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">تاريخ الإنشاء:</span>
                    <span className="font-medium">
                      {formatRelativeTime(task.created_at)}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <div className="flex justify-between w-full">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              <X className="h-4 w-4 ml-2" />
              إغلاق
            </Button>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onEdit}
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={onDelete}
              >
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

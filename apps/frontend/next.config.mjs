/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // Keep disabled for now to avoid blocking builds
  },
  typescript: {
    ignoreBuildErrors: true, // Keep disabled for now to avoid blocking builds
  },
  images: {
    unoptimized: true,
  },
  // Professional development configuration
  experimental: {
    allowedDevOrigins: [
      'mtbrmg.local:3001',
      'app.mtbrmg.local:3001',
      'dashboard.mtbrmg.local:3001',
      'localhost:3001'
    ],
  },
  // API rewrites removed - using direct backend connections for local development
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // Performance optimizations
  poweredByHeader: false,
  compress: true,
  // Development optimizations
  swcMinify: true,
  reactStrictMode: true,
}

export default nextConfig

# MTBRMG ERP System

## Overview

A comprehensive RTL (Right-to-Left) ERP system designed specifically for Egyptian digital agencies. Built with Django REST Framework backend and Next.js frontend, featuring a unified founder dashboard architecture with Arabic-first design.

## 🚀 Quick Start (Local Development)

### Prerequisites
- macOS (tested on 10.15+)
- Homebrew package manager
- Node.js 18+ with pnpm
- Python 3.11+

### Automated Setup
```bash
# Clone the repository
git clone <repository-url>
cd mtbrmg-erp-system

# Run automated setup (installs PostgreSQL, Redis, dependencies)
./scripts/setup-local.sh

# Start all services
./scripts/start-local.sh
```

### Access the Application
- **Frontend**: http://localhost:3001/founder-dashboard
- **Login**: founder / demo123
- **Backend API**: http://localhost:8000/api/
- **Admin Panel**: http://localhost:8000/admin/

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 15.2.4 with TypeScript
- **Backend**: Django 4.2.9 with Django REST Framework
- **Database**: PostgreSQL 15
- **Cache/Queue**: Redis 7
- **Task Queue**: Celery with Redis broker
- **Styling**: Tailwind CSS with RTL support
- **Authentication**: JWT with extended sessions

### Project Structure
```
mtbrmg-erp-system/
├── apps/
│   ├── frontend/          # Next.js application
│   └── backend/           # Django API server
├── packages/
│   ├── shared/            # Common types and utilities
│   ├── config/            # Shared configurations
│   └── ui/                # Shared UI components
├── scripts/               # Development scripts
└── docs/                  # Documentation
```

## 🔧 Development

### Manual Setup (Alternative)
If the automated script fails, you can set up manually:

```bash
# Install services
brew install postgresql@15 redis
brew services start postgresql@15 redis

# Create database
createdb mtbrmg_erp

# Backend setup
cd apps/backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
cp .env.local .env
python manage.py migrate
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('founder', '<EMAIL>', 'demo123')"

# Frontend setup
cd ../frontend
pnpm install
```

### Development Commands
```bash
# Start all services
npm run start:local

# Individual services
npm run dev:backend     # Django backend only
npm run dev:frontend    # Next.js frontend only

# Database operations
npm run migrate         # Run Django migrations
npm run create-founder  # Create founder user

# Development tools
npm run build          # Build all applications
npm run lint           # Lint all code
npm run test           # Run tests
```

### Service Ports
| Service | Port | URL |
|---------|------|-----|
| PostgreSQL | 5432 | localhost:5432 |
| Redis | 6379 | localhost:6379 |
| Django Backend | 8000 | http://localhost:8000 |
| Next.js Frontend | 3001 | http://localhost:3001 |

## 🎯 Features

### Core Functionality
- **Unified Founder Dashboard**: Single dashboard architecture (no admin/user separation)
- **Client Management**: Comprehensive client profiles and contact management
- **Project Management**: Project creation, tracking, and milestone management
- **Task Management**: Task assignment, progress tracking, and deadlines
- **Team Management**: Team member profiles and role management
- **Single-Step Workflows**: Unified client/project creation with radio button options

### Technical Features
- **RTL Support**: Full Arabic language support with RTL layout
- **Responsive Design**: Mobile-first responsive interface
- **Real-time Updates**: Live data synchronization
- **Extended Sessions**: Long-lasting authentication for development
- **API Documentation**: Comprehensive REST API with Django Ninja
- **Type Safety**: Full TypeScript implementation

### Custom Branding
- **Animated Logo**: Custom sidebar logo with animations
- **Profile Customization**: Custom profile images and Arabic names
- **Localized Interface**: Arabic-first design with Egyptian market focus

## 🔐 Authentication

### Default Credentials
- **Username**: founder
- **Password**: demo123
- **Email**: <EMAIL>
- **Role**: Founder (full system access)

### Session Configuration
- **Session Duration**: Extended for development (1 year)
- **JWT Tokens**: 24-hour access tokens, 30-day refresh tokens
- **Auto-login**: Sessions persist across browser restarts

## 📊 Database Schema

### Core Models
- **User**: Extended user model with founder role
- **Client**: Client information and contact details
- **Project**: Project management with client relationships
- **Task**: Task tracking with assignments and deadlines
- **TeamMember**: Team member profiles and roles

### Relationships
- Clients → Projects (One-to-Many)
- Projects → Tasks (One-to-Many)
- Users → Tasks (Many-to-Many assignments)
- Users → TeamMembers (One-to-One profiles)

## 🛠️ Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using ports
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :8000  # Backend
lsof -i :3001  # Frontend
```

#### Service Issues
```bash
# Restart services
brew services restart postgresql@15
brew services restart redis

# Check service status
brew services list | grep -E "(postgresql|redis)"
```

#### Database Issues
```bash
# Reset database
dropdb mtbrmg_erp
createdb mtbrmg_erp
cd apps/backend && source venv/bin/activate
python manage.py migrate
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('founder', '<EMAIL>', 'demo123')"
```

### Getting Help
1. Check the [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md)
2. Review logs in `apps/backend/logs/django.log`
3. Ensure all services are running with `brew services list`

## 📚 Documentation

- [Local Development Guide](LOCAL_DEVELOPMENT_GUIDE.md) - Comprehensive setup instructions
- [Docker Transition Analysis](DOCKER_TO_LOCAL_TRANSITION_ANALYSIS.md) - Migration documentation
- [API Documentation](http://localhost:8000/api/) - Interactive API docs (when running)

## 🚀 Deployment

This system is configured for local development. For production deployment:
1. Update environment variables for production
2. Configure proper database and Redis instances
3. Set up proper SSL certificates
4. Configure static file serving
5. Set up monitoring and logging

## 📄 License

Private project for MTBRMG digital agency.

---

**Last Updated**: Current  
**Environment**: Local Development  
**Status**: Production Ready

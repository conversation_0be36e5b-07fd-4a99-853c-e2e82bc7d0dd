# 🚀 MTBRMG ERP - Performance Solutions for macOS

## 🔥 المشكلة المكتشفة

نظامك يعاني من:
- **Load Average**: 100.78 (عالي جداً! الطبيعي < 8)
- **Memory Pressure**: عالي
- **Next.js Development Mode**: بطيء بطبيعته

## ⚡ الحلول السريعة (اختر واحد)

### 1. 🏎️ الحل الأسرع: Production Build محلي

```bash
# إعداد واحد فقط
./scripts/setup-prod-local.sh

# تشغيل سريع (5-10x أسرع)
./scripts/prod-local.sh
```

**المميزات:**
- ⚡ **10x أسرع** من development mode
- 🚀 **استهلاك ذاكرة أقل** بـ 70%
- 💾 **تحميل فوري** للصفحات
- 🔥 **أداء production** مع سهولة التطوير

### 2. 🏁 الحل الثاني: Vite Ultra-Fast

```bash
# إعداد Vite (أسرع من Next.js)
./scripts/setup-vite-dev.sh

# تشغيل بـ Vite
./scripts/vite-dev.sh
```

**المميزات:**
- ⚡ **Hot reload فوري** (< 100ms)
- 🚀 **بدء سريع** (< 2 ثانية)
- 💾 **استهلاك ذاكرة أقل** بـ 60%
- 🔥 **تجربة تطوير محسنة**

### 3. 🔧 الحل الثالث: تحسين النظام الحالي

```bash
# تحسين فوري
./scripts/instant-performance-fix.sh

# تشغيل محسن
./scripts/fast-dev.sh
```

**المميزات:**
- ⚡ **تحسين فوري** للنظام الحالي
- 🧠 **تحسين الذاكرة**
- 🚫 **إيقاف العمليات غير الضرورية**
- 📊 **تحسين قاعدة البيانات**

### 4. 🍎 الحل الرابع: تحسين macOS

```bash
# تحسين النظام
./scripts/optimize-macos.sh

# مراقبة الأداء
./scripts/monitor-performance.sh

# تنظيف النظام
./scripts/cleanup-system.sh
```

**المميزات:**
- 🔍 **إيقاف Spotlight** للمجلدات التطويرية
- ⏰ **استبعاد من Time Machine**
- 🌐 **تحسين DNS**
- 📁 **زيادة حدود الملفات**

## 🎯 التوصية الشخصية

### للسرعة القصوى:
```bash
# 1. تحسين النظام أولاً
./scripts/optimize-macos.sh

# 2. إعداد Production Build
./scripts/setup-prod-local.sh

# 3. تشغيل سريع
./scripts/prod-local.sh
```

### للتطوير المريح:
```bash
# 1. إعداد Vite
./scripts/setup-vite-dev.sh

# 2. تشغيل Vite
./scripts/vite-dev.sh
```

## 📊 مقارنة الأداء

| الحل | سرعة البدء | Hot Reload | استهلاك الذاكرة | سهولة الاستخدام |
|------|------------|------------|-----------------|------------------|
| **Production Local** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Vite** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Next.js محسن** | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Next.js عادي** | ⭐⭐ | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🛠️ أدوات المراقبة

### مراقبة الأداء المباشر:
```bash
./scripts/monitor-performance.sh
```

### اختبار الاتصال:
```bash
./scripts/test-api-connectivity.sh
```

### تنظيف النظام:
```bash
./scripts/cleanup-system.sh
```

## 🚨 حلول الطوارئ

### إذا كان النظام بطيء جداً:
```bash
# إعادة تشغيل الخدمات
sudo brew services restart postgresql@15
sudo brew services restart redis

# تنظيف الذاكرة
sudo purge

# إيقاف العمليات الثقيلة
pkill -f "Google Chrome Helper"
pkill -f "Spotlight"
```

### إذا فشل كل شيء:
```bash
# العودة للـ localhost البسيط
export NEXT_PUBLIC_API_URL=http://localhost:8000/api
cd apps/frontend && pnpm dev
```

## 🎯 الخلاصة

**للحصول على أفضل أداء:**

1. **ابدأ بـ**: `./scripts/optimize-macos.sh`
2. **ثم استخدم**: `./scripts/setup-prod-local.sh`
3. **شغل بـ**: `./scripts/prod-local.sh`

هذا سيعطيك **أداء production** مع **سهولة التطوير**!

---

**💡 نصيحة**: إذا كنت تريد أسرع حل ممكن، استخدم Production Build محلي. إذا كنت تريد أفضل تجربة تطوير، استخدم Vite.

# MTBRMG ERP - Local Development Guide

## Overview

This guide provides instructions for setting up and running the MTBRMG ERP system in a local macOS development environment. The system has been transitioned from Docker-based development to native local services.

## Prerequisites

### Required Software
- **macOS** (tested on macOS 10.15+)
- **Homebrew** package manager
- **Node.js** 18+ (with pnpm)
- **Python** 3.11+
- **Git**

### Install Homebrew (if not installed)
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

## Quick Start

### 1. Automated Setup (Recommended)
Run the automated setup script to install all dependencies and configure the environment:

```bash
# Make sure you're in the project root
cd /path/to/mtbrmg-erp-system

# Run the setup script
./scripts/setup-local.sh
```

This script will:
- Install PostgreSQL 15 and Redis via Homebrew
- Create Python virtual environment
- Install Python dependencies
- Install Node.js dependencies with pnpm
- Create the database and run migrations
- Create the founder user account

### 2. Start Development Servers
```bash
# Start all services (PostgreSQL, Redis, Backend, Frontend)
./scripts/start-local.sh
```

### 3. Access the Application
- **Frontend**: http://localhost:3001/founder-dashboard
- **Login**: founder / demo123
- **Backend API**: http://localhost:8000/api/
- **Admin Panel**: http://localhost:8000/admin/

## Manual Setup (Alternative)

If you prefer to set up manually or the automated script fails:

### 1. Install Services
```bash
# Install PostgreSQL
brew install postgresql@15
brew services start postgresql@15

# Install Redis
brew install redis
brew services start redis

# Create database
createdb mtbrmg_erp
```

### 2. Setup Backend
```bash
cd apps/backend

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.local .env

# Run migrations
python manage.py migrate

# Create founder user
python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
User.objects.create_superuser('founder', '<EMAIL>', 'demo123')
"

# Start backend server
python manage.py runserver 8000
```

### 3. Setup Frontend
```bash
# In a new terminal
cd apps/frontend

# Install dependencies
pnpm install

# Start frontend server
pnpm dev
```

## Development Workflow

### Daily Development
```bash
# Start all services
npm run start:local

# Or start services individually:
npm run dev:backend    # Start Django backend
npm run dev:frontend   # Start Next.js frontend
```

### Database Operations
```bash
# Run migrations
npm run migrate

# Create founder user (if needed)
npm run create-founder

# Access database directly
psql -h localhost -p 5432 -U postgres -d mtbrmg_erp
```

### Service Management
```bash
# Check service status
brew services list | grep -E "(postgresql|redis)"

# Start/stop services manually
brew services start postgresql@15
brew services stop postgresql@15
brew services start redis
brew services stop redis
```

## Configuration

### Environment Variables

#### Backend (.env)
```env
DEBUG=True
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mtbrmg_erp
DB_USER=postgres
DB_PASSWORD=postgres
REDIS_URL=redis://localhost:6379/0
```

#### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3001
```

## Service Ports

| Service | Port | URL |
|---------|------|-----|
| PostgreSQL | 5432 | localhost:5432 |
| Redis | 6379 | localhost:6379 |
| Django Backend | 8000 | http://localhost:8000 |
| Next.js Frontend | 3001 | http://localhost:3001 |

## Troubleshooting

### Common Issues

#### Port Conflicts
If you get port conflicts, check what's running:
```bash
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
lsof -i :8000  # Backend
lsof -i :3001  # Frontend
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
brew services list | grep postgresql

# Restart PostgreSQL
brew services restart postgresql@15

# Test connection
pg_isready -h localhost -p 5432
```

#### Redis Connection Issues
```bash
# Check Redis status
brew services list | grep redis

# Test Redis connection
redis-cli -h localhost -p 6379 ping
```

#### Python Environment Issues
```bash
cd apps/backend
source venv/bin/activate
pip install -r requirements.txt
```

### Reset Everything
If you need to start fresh:
```bash
# Stop all services
brew services stop postgresql@15
brew services stop redis

# Remove database
dropdb mtbrmg_erp

# Recreate database
createdb mtbrmg_erp

# Re-run setup
./scripts/setup-local.sh
```

## Features Preserved

✅ **Unified Founder Dashboard Architecture**
- Single dashboard type (no admin/user dashboards)
- Founder-centric navigation and features

✅ **Authentication System**
- Login: <EMAIL> / demo123
- Extended session timeouts for development

✅ **Single-Step Workflows**
- Unified client/project creation form
- Radio button options for existing vs new clients

✅ **Custom Branding**
- Animated sidebar logo (/the_logo.png)
- Custom profile image (/myimage.jpg)
- Arabic profile name: 'محمد عبد الفتاح'

## Development Tools

### Useful Commands
```bash
# View logs
tail -f apps/backend/logs/django.log

# Django shell
cd apps/backend && source venv/bin/activate && python manage.py shell

# Database shell
psql -h localhost -p 5432 -U postgres -d mtbrmg_erp

# Redis CLI
redis-cli -h localhost -p 6379
```

### API Testing
```bash
# Test API endpoints
curl http://localhost:8000/api/health/
curl http://localhost:8000/api/auth/login/
curl http://localhost:8000/api/clients/
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the logs in `apps/backend/logs/django.log`
3. Ensure all services are running with `brew services list`

---

**Last Updated**: Current
**Environment**: macOS Local Development
**Status**: Production Ready

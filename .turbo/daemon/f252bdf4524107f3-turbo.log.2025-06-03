2025-06-03T20:04:26.950881Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/cookies/.turbo-cookie"), AnchoredSystemPathBuf(".turbo/cookies/1.cookie")}
2025-06-03T20:04:26.950888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T20:04:31.652642Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/mtbrmg_erp/__pycache__/celery.cpython-312.pyc.4408134032"), AnchoredSystemPathBuf("apps/backend/mtbrmg_erp/__pycache__/celery.cpython-312.pyc"), AnchoredSystemPathBuf("apps/backend/mtbrmg_erp/__pycache__/__init__.cpython-312.pyc.4408133712"), AnchoredSystemPathBuf("apps/backend/mtbrmg_erp/__pycache__/__init__.cpython-312.pyc")}
2025-06-03T20:04:31.653203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:04:36.117225Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.5016ff75fe829f83.hot-update.js"), AnchoredSystemPathBuf("packages/shared/dist/types/clients.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("packages/shared/dist/types/projects.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("packages/shared/dist/types/tasks.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("packages/shared/dist/types/auth.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/utils.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/51765a0444ca5db2.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/5016ff75fe829f83.webpack.hot-update.json"), AnchoredSystemPathBuf("packages/shared/dist/types/auth.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/projects/new/page.4e193a449675ee83.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.5016ff75fe829f83.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("packages/shared/dist/types/tasks.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/4e193a449675ee83.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/constants/index.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/index.d.ts"), AnchoredSystemPathBuf("packages/shared/dist/types/index.js"), AnchoredSystemPathBuf("apps/backend/logs/django.log"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.4e193a449675ee83.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("packages/shared/dist/types/clients.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/55daff522583b502.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.51765a0444ca5db2.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("packages/shared/dist/types/projects.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.55daff522583b502.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("packages/shared/dist/constants/index.js"), AnchoredSystemPathBuf("packages/shared/dist/types/index.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.4e193a449675ee83.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("packages/shared/dist/index.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("packages/shared/dist/utils.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json")}
2025-06-03T20:04:36.117256Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Other("@mtbrmg/shared"), path: AnchoredSystemPathBuf("packages/shared") }, WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:04:36.120603Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:37.555144Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>")}
2025-06-03T20:05:37.556309Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:37.602939Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:38.451175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js")}
2025-06-03T20:05:38.451189Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:38.451375Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:38.851102Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/8.pack.gz_")}
2025-06-03T20:05:38.851130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:38.851223Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:39.050101Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/4b19cf292706377d.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.4b19cf292706377d.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.4b19cf292706377d.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js")}
2025-06-03T20:05:39.050111Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:39.072754Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:39.149457Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/8.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz")}
2025-06-03T20:05:39.149464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:39.178398Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:39.250501Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T20:05:39.250509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:39.250572Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:40.349975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_")}
2025-06-03T20:05:40.349994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:40.360210Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:40.450419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_")}
2025-06-03T20:05:40.450425Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:40.450466Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:05:40.650471Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T20:05:40.650479Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:05:40.660388Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:04.255140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>")}
2025-06-03T20:06:04.256010Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:04.256242Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:04.451097Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.03ccbf926a8e4ef2.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/03ccbf926a8e4ef2.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js")}
2025-06-03T20:06:04.451108Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:04.451160Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:06.250792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/db.sqlite3"), AnchoredSystemPathBuf("apps/backend/db.sqlite3-journal")}
2025-06-03T20:06:06.250909Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:06:11.059731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/cd1acdae9ce424ac.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.cd1acdae9ce424ac.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2c91949449b75258.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2c91949449b75258.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>")}
2025-06-03T20:06:11.059757Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:11.059801Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:11.550427Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/9.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz_")}
2025-06-03T20:06:11.550436Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:11.550495Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:11.649983Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/9.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_")}
2025-06-03T20:06:11.649992Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:11.650038Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:11.749872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_")}
2025-06-03T20:06:11.749879Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:11.749927Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:11.849280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/9.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/9.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old")}
2025-06-03T20:06:11.849289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:11.849341Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:12.050673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts")}
2025-06-03T20:06:12.050685Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:12.050734Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:12.149567Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.05883239857d8a1e.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/05883239857d8a1e.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json")}
2025-06-03T20:06:12.149577Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:12.149619Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:15.149402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js")}
2025-06-03T20:06:15.149426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:15.149514Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:15.249640Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/aebe5f5d9d5873a1.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.aebe5f5d9d5873a1.hot-update.js")}
2025-06-03T20:06:15.249653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:15.249733Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:41.349206Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js")}
2025-06-03T20:06:41.349224Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:41.581814Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:41.582003Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2a2642927dc4584f.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2a2642927dc4584f.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts")}
2025-06-03T20:06:41.582010Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:41.582037Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:42.449211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_")}
2025-06-03T20:06:42.449221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:42.449270Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:42.548839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_")}
2025-06-03T20:06:42.548847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:42.548882Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:42.649877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz")}
2025-06-03T20:06:42.649887Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:42.649926Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:06:42.749297Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T20:06:42.749302Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:06:42.749333Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:07:00.350132Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log"), AnchoredSystemPathBuf("apps/frontend/.next/trace")}
2025-06-03T20:07:00.350153Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }, WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:07:00.350190Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:09:56.555934Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("task-system-analysis.md")}
2025-06-03T20:09:56.556674Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T20:17:34.700115Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:17:34.701082Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:18:00.894683Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:18:00.895440Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:18:46.992906Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/edit-task-form.tsx")}
2025-06-03T20:18:46.992931Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:19:12.398452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/edit-task-form.tsx")}
2025-06-03T20:19:12.399885Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:19:35.692435Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/dialogs/delete-task-dialog.tsx")}
2025-06-03T20:19:35.692467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:20:21.392186Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/dialogs/task-details-dialog.tsx")}
2025-06-03T20:20:21.392209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:20:47.292267Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:20:47.292290Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:21:04.592675Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:21:04.592694Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:21:23.492748Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:21:23.492774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:21:36.192067Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:21:36.192092Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:21:59.492033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:21:59.492061Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:22:12.192677Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:22:12.192694Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:22:28.592451Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:22:28.592577Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:22:46.192593Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:22:46.192616Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:23:11.992620Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/aebe5f5d9d5873a1.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/05883239857d8a1e.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.cd1acdae9ce424ac.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.4b19cf292706377d.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2c91949449b75258.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2c91949449b75258.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/4b19cf292706377d.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2a2642927dc4584f.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2a2642927dc4584f.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.4b19cf292706377d.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.05883239857d8a1e.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/03ccbf926a8e4ef2.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/cd1acdae9ce424ac.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.03ccbf926a8e4ef2.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.aebe5f5d9d5873a1.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>")}
2025-06-03T20:23:11.992661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:23:11.993358Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:23:12.292517Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json")}
2025-06-03T20:23:12.292527Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:23:12.292584Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:23:12.491828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js")}
2025-06-03T20:23:12.491847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:23:12.491946Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:23:12.592265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js")}
2025-06-03T20:23:12.592273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:23:12.592324Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:23:39.592704Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:23:39.592735Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:24:34.594654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz_")}
2025-06-03T20:24:34.595039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:34.595168Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:34.892714Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_")}
2025-06-03T20:24:34.892722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:34.896085Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:34.992793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz")}
2025-06-03T20:24:34.992808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:34.992910Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:35.996075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/main.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/amp.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages/_app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/react-refresh.js"), AnchoredSystemPathBuf("apps/frontend/.next/fallback-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/main-app.js")}
2025-06-03T20:24:35.996089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:35.996155Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.092294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback")}
2025-06-03T20:24:37.092417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.092603Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.291888Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/index.pack.gz_")}
2025-06-03T20:24:37.291898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.291980Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.691407Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_")}
2025-06-03T20:24:37.691425Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.719341Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.791311Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_")}
2025-06-03T20:24:37.791319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.791365Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.891767Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_")}
2025-06-03T20:24:37.891774Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.891822Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:37.991701Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/13.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/10.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz")}
2025-06-03T20:24:37.991710Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:37.991752Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:38.091172Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development-fallback/index.pack.gz_")}
2025-06-03T20:24:38.091181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:24:38.091236Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:24:41.691652Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:24:41.691672Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:25:36.895009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T20:25:36.895311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:25:36.896130Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:25:36.991576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz")}
2025-06-03T20:25:36.991583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:25:36.991645Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:25:52.491116Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:25:52.491144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:26:04.591735Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:26:04.591755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:26:37.615416Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T20:26:37.615439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:26:38.214925Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_document.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_app.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json")}
2025-06-03T20:26:38.214934Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:26:38.214976Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:38.436251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_")}
2025-06-03T20:27:38.437145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:38.443169Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:38.732321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_")}
2025-06-03T20:27:38.732332Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:38.732414Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:38.832987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_")}
2025-06-03T20:27:38.832995Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:38.833044Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:38.933347Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_")}
2025-06-03T20:27:38.933355Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:38.933408Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:39.032926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_")}
2025-06-03T20:27:39.032932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:39.032966Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:27:39.233053Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_")}
2025-06-03T20:27:39.233063Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:27:39.233100Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:28:00.935935Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts")}
2025-06-03T20:28:00.936394Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:28:00.939993Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:01.137211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_")}
2025-06-03T20:29:01.138328Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:29:01.161996Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:01.234162Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_")}
2025-06-03T20:29:01.234177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:29:01.234902Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:01.333708Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_")}
2025-06-03T20:29:01.333718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:29:01.345502Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:01.434766Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz")}
2025-06-03T20:29:01.434776Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:29:01.435106Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:01.534113Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz")}
2025-06-03T20:29:01.534121Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:29:01.534164Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:29:34.834182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/hooks/use-projects.ts")}
2025-06-03T20:29:34.834229Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:06.934120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/hooks/use-projects.ts")}
2025-06-03T20:30:06.934143Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.233590Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages/_app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/react-refresh.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2")}
2025-06-03T20:30:07.233603Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.233662Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:07.334652Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json")}
2025-06-03T20:30:07.334662Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.335718Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:07.535255Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/76a0395d556289dc.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.76a0395d556289dc.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js")}
2025-06-03T20:30:07.535265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.535322Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:07.634028Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts")}
2025-06-03T20:30:07.634037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.651610Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:07.733745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>")}
2025-06-03T20:30:07.733760Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:07.733821Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:08.134834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js")}
2025-06-03T20:30:08.134859Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:08.135062Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:08.835120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>")}
2025-06-03T20:30:08.835150Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:08.835334Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:10.134177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json")}
2025-06-03T20:30:10.134223Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:10.134418Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:10.335030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ce619c7b82688e90.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ce619c7b82688e90.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js")}
2025-06-03T20:30:10.335038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:10.335091Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:11.337580Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_")}
2025-06-03T20:30:11.337654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:11.346465Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:11.434299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T20:30:11.434306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:11.434349Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:30:11.634417Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/17.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/15.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/16.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/13.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/18.pack.gz")}
2025-06-03T20:30:11.634426Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:30:11.634631Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:31:10.635461Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz_")}
2025-06-03T20:31:10.635571Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:31:10.635954Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:31:10.734757Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_")}
2025-06-03T20:31:10.734764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:31:10.734807Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:31:10.934480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/11.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/12.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/14.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old")}
2025-06-03T20:31:10.934489Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:31:10.934731Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:32:20.635462Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/trace")}
2025-06-03T20:32:20.635483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:32:20.635596Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:32:25.535612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:32:25.535621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:33:05.035848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T20:33:05.035867Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:35:06.324526Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/media"), AnchoredSystemPathBuf("apps/backend/staticfiles")}
2025-06-03T20:35:06.339213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:35:21.010888Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles"), AnchoredSystemPathBuf("apps/backend/media"), AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:35:21.013118Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:35:21.016497Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:36:31.642978Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:36:31.643504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:38:22.741365Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:38:22.741625Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:38:40.739787Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:38:40.739808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:38:57.537904Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:38:57.537924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:39:15.839251Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:39:15.839269Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:39:34.138344Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:39:34.138364Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:39:52.039504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:39:52.039528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:40:05.139530Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/_events.json")}
2025-06-03T20:40:05.139562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:40:05.139637Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:40:05.338385Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/_events.json")}
2025-06-03T20:40:05.338394Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:40:05.338462Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T20:40:16.739799Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T20:40:16.739821Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T20:51:53.724884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:51:53.727312Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:52:07.109029Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:52:07.109206Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:52:31.706301Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:52:31.706400Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:53:15.307870Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:53:15.308090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:53:31.911121Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:53:31.911245Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:54:02.515682Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:54:02.516923Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:54:17.809556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:54:17.809588Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:54:34.409755Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T20:54:34.409792Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:54:48.508959Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:54:48.508979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:55:11.009875Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:55:11.009899Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:55:28.510610Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:55:28.510657Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:55:45.911004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:55:45.911056Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T20:56:09.215431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T20:56:09.216000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:08:17.251821Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:08:17.253562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:08:31.747285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:08:31.747310Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:08:42.946667Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:08:42.946688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:09:27.853063Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:09:27.854284Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:09:49.748070Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:09:49.748094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:11:03.253724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:11:03.254198Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:11:21.853409Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:11:21.854040Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:11:34.650810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:11:34.651113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:11:48.450088Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:11:48.450113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:12:01.550439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:12:01.550461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:12:25.342100Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("localhost.har")}
2025-06-03T21:12:25.342914Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:16:24.238583Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:16:24.241618Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:16:48.333750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/auth-provider.tsx")}
2025-06-03T21:16:48.333791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:17:32.532527Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/stores/auth-store.ts")}
2025-06-03T21:17:32.532553Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:19:24.173073Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("test-auth.js")}
2025-06-03T21:19:24.174591Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:20:04.444010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("test-auth.html")}
2025-06-03T21:20:04.450841Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:20:11.237359Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("test-auth.html")}
2025-06-03T21:20:11.237400Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:20:35.935596Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:20:35.935817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:21:00.735573Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:21:00.735595Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:22:29.343309Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:22:29.344186Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:22:40.237872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("test-auth.html"), AnchoredSystemPathBuf("test-auth.js")}
2025-06-03T21:22:40.237904Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:36:08.635933Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("MTBRMG_ERP_TASK_SYSTEM_ANALYSIS.md")}
2025-06-03T21:36:08.637642Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:37:26.885449Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/hooks/use-tasks.ts")}
2025-06-03T21:37:26.885916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:37:40.069864Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/hooks/use-tasks.ts")}
2025-06-03T21:37:40.069909Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:37:56.168940Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/hooks/use-tasks.ts")}
2025-06-03T21:37:56.168966Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:38:36.074140Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:38:36.074915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:38:50.570819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:38:50.570882Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:39:04.372081Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:39:04.372205Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:39:23.970848Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:39:23.970862Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:40:17.672031Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:40:17.672092Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:40:29.472266Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/forms/add-task-form.tsx")}
2025-06-03T21:40:29.472298Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:41:29.182014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("MTBRMG_ERP_TASK_SYSTEM_ANALYSIS.md")}
2025-06-03T21:41:29.183988Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:43:43.890413Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("network.har")}
2025-06-03T21:43:43.891653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T21:45:41.184414Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:45:41.184810Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:45:55.782393Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:45:55.782557Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:06.882284Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:46:06.882301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:25.685049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:46:25.685086Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:39.083656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:46:39.083866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:51.184391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:46:51.184414Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:51.284715Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:46:51.284723Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:46:51.284763Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T21:47:17.685372Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:47:17.685390Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:47:34.084931Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/frontend/.next/_events.json")}
2025-06-03T21:47:34.085340Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:47:34.086779Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T21:47:34.185379Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/_events.json")}
2025-06-03T21:47:34.185393Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:47:34.185763Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T21:53:26.299821Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:53:26.300237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:59:11.680206Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:59:11.688713Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:59:12.005176Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T21:59:12.005185Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T21:59:12.006791Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:01:25.704341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:01:25.704952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:01:58.703133Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:01:58.703299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:02:10.808510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:02:10.808926Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:02:28.402566Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:02:28.402593Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:02:48.403834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:02:48.403866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:03:09.304603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/app/founder-dashboard/tasks/page.tsx")}
2025-06-03T22:03:09.304622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:03:56.739235Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/.api.ts.swp")}
2025-06-03T22:03:56.753025Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:08:06.515861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T22:08:06.517239Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:12:23.710576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T22:12:23.711177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:12:35.910354Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T22:12:35.910389Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:12:52.505992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("lib/api.ts")}
2025-06-03T22:12:52.506018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T22:13:03.507065Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("lib/api.ts")}
2025-06-03T22:13:03.507097Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T22:14:33.115152Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:14:33.116196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:14:33.206906Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:14:33.206915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:14:33.207203Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:15:10.108216Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.env.docker")}
2025-06-03T22:15:10.108237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:15:10.117918Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:15:23.107925Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:15:23.107950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:18:27.220033Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker/Dockerfile.frontend")}
2025-06-03T22:18:27.222829Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T22:20:31.122760Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:20:31.124750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:20:31.314793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:20:31.314807Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:20:31.328938Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:23:08.030486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:23:08.030987Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:25:20.028556Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:25:20.029803Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:28:05.824886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:28:05.825955Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:34:55.136217Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.env.docker")}
2025-06-03T22:34:55.137701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:34:55.188746Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:42:27.166024Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T22:42:27.231319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:42:52.192666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/src/app"), AnchoredSystemPathBuf("apps/frontend/src/app/api"), AnchoredSystemPathBuf("apps/frontend/src")}
2025-06-03T22:42:52.210248Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:42:54.456695Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/src/app/api"), AnchoredSystemPathBuf("apps/frontend/src/app/api/test"), AnchoredSystemPathBuf("apps/frontend/src/app/api/test/route.ts")}
2025-06-03T22:42:54.457879Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:42:54.461246Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T22:47:49.825224Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/src/app/api/test/route.ts")}
2025-06-03T22:47:49.826755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T22:57:26.588168Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".DS_Store")}
2025-06-03T22:57:26.590029Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T22:58:17.774627Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("node_modules/.DS_Store"), AnchoredSystemPathBuf(".turbo/.DS_Store"), AnchoredSystemPathBuf("packages/.DS_Store"), AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf(".DS_Store"), AnchoredSystemPathBuf("apps/backend/.DS_Store")}
2025-06-03T22:58:17.774685Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T22:58:22.774304Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/.DS_Store"), AnchoredSystemPathBuf("apps/backend/.DS_Store"), AnchoredSystemPathBuf("apps/backend/venv/.DS_Store"), AnchoredSystemPathBuf(".DS_Store")}
2025-06-03T22:58:22.774319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:04:25.285403Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("deep_error_analysis.md"), AnchoredSystemPathBuf("demo_2fa_setup.py"), AnchoredSystemPathBuf("final_cache_cleaner.py"), AnchoredSystemPathBuf("docker-compose.prod.yml"), AnchoredSystemPathBuf("docker_console_fix.py"), AnchoredSystemPathBuf("fix_console_errors.py"), AnchoredSystemPathBuf("BUTTON_FUNCTIONALITY_IMPLEMENTATION_REPORT.md"), AnchoredSystemPathBuf("find_dashboard_references.py"), AnchoredSystemPathBuf("docker-compose.yml"), AnchoredSystemPathBuf("IMPLEMENTATION_SUMMARY.md")}
2025-06-03T23:04:25.286355Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:04:25.378146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("PHASE_2_NEW_WORKFLOW_DESIGN.md"), AnchoredSystemPathBuf("localhost.har"), AnchoredSystemPathBuf("MTBRMG_ERP_DEEP_ANALYSIS.md"), AnchoredSystemPathBuf("PHASE_1_CURRENT_STATE_ANALYSIS.md"), AnchoredSystemPathBuf("MTBRMG_ERP_IMPLEMENTATION_SUMMARY.md"), AnchoredSystemPathBuf("MTBRMG_ERP_RECOMMENDATIONS.md"), AnchoredSystemPathBuf("MTBRMG_ERP_CRITICAL_AUDIT_REPORT.md"), AnchoredSystemPathBuf("UNIFIED_WORKFLOW_IMPLEMENTATION_SUMMARY.md"), AnchoredSystemPathBuf("test-project-creation.html"), AnchoredSystemPathBuf("task-system-analysis.md"), AnchoredSystemPathBuf("logo-enhancement-summary.md"), AnchoredSystemPathBuf("MTBRMG_ERP_TASK_SYSTEM_ANALYSIS.md"), AnchoredSystemPathBuf("MTBRMG_BUTTON_FUNCTIONALITY_ANALYSIS.md"), AnchoredSystemPathBuf("PHASE_3_IMPLEMENTATION_PLAN.md"), AnchoredSystemPathBuf("projects_page_error_fix_analysis.md"), AnchoredSystemPathBuf("MTBRMG_ERP_COMPREHENSIVE_AUDIT_REPORT.md"), AnchoredSystemPathBuf("PRODUCTION_DEPLOYMENT.md"), AnchoredSystemPathBuf("login_error_fix_analysis.md"), AnchoredSystemPathBuf("test_implementation.py")}
2025-06-03T23:04:25.378163Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:05:06.988550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("DOCKER_TO_LOCAL_TRANSITION_ANALYSIS.md")}
2025-06-03T23:05:06.989418Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:05:30.277746Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("DOCKER_TO_LOCAL_TRANSITION_ANALYSIS.md")}
2025-06-03T23:05:30.277778Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:06:54.986396Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker/Dockerfile.backend"), AnchoredSystemPathBuf("docker/Dockerfile.frontend")}
2025-06-03T23:06:54.987582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:06:55.079234Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker/init-db.sql"), AnchoredSystemPathBuf("scripts/monitor.sh"), AnchoredSystemPathBuf("scripts/deploy.sh"), AnchoredSystemPathBuf("docker/docker-compose.yml"), AnchoredSystemPathBuf("docker/start-backend.sh")}
2025-06-03T23:06:55.079242Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:07:06.978501Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/Dockerfile.prod"), AnchoredSystemPathBuf("apps/frontend/Dockerfile.prod")}
2025-06-03T23:07:06.978646Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:07:21.779371Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.dockerignore")}
2025-06-03T23:07:21.779403Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:07:33.978776Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docker")}
2025-06-03T23:07:33.978799Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:07:47.479108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/.env.docker"), AnchoredSystemPathBuf("apps/frontend/.env.docker")}
2025-06-03T23:07:47.479130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:07:51.235523Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:08:06.078935Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/.env.local")}
2025-06-03T23:08:06.078969Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:08:31.679902Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.env.local")}
2025-06-03T23:08:31.680020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:08:31.725946Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:08:31.779046Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.env.local")}
2025-06-03T23:08:31.779053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:08:31.779120Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:09:02.979355Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/.env")}
2025-06-03T23:09:02.979382Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:09:29.580102Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/setup-local.sh")}
2025-06-03T23:09:29.580137Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:09:58.080474Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/start-local.sh")}
2025-06-03T23:09:58.080493Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:10:11.780307Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/setup-local.sh"), AnchoredSystemPathBuf("scripts/start-local.sh")}
2025-06-03T23:10:11.780344Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:10:33.781721Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-06-03T23:10:33.781738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-06-03T23:11:15.882926Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("LOCAL_DEVELOPMENT_GUIDE.md")}
2025-06-03T23:11:15.882952Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:13:02.789321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("init-db.sql")}
2025-06-03T23:13:02.789924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:13:48.991648Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("README.md")}
2025-06-03T23:13:48.993661Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:14:28.990626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/setup-local.sh")}
2025-06-03T23:14:28.991222Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:14:44.884213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/start-local.sh")}
2025-06-03T23:14:44.884247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:15:34.984273Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/.env.local")}
2025-06-03T23:15:34.984562Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:15:46.984961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/.env")}
2025-06-03T23:15:46.984989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:21:31.595591Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("scripts/setup-local.sh")}
2025-06-03T23:21:31.596539Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:22:21.798866Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("TRANSITION_COMPLETE_SUMMARY.md")}
2025-06-03T23:22:21.799704Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:27:32.597964Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.env.local")}
2025-06-03T23:27:32.598920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:27:32.616721Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:27:56.094493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("lib/api.ts")}
2025-06-03T23:27:56.094539Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:28:24.294446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/lib/api.ts")}
2025-06-03T23:28:24.294471Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:28:48.195144Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/next.config.mjs")}
2025-06-03T23:28:48.195160Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:30:17.002110Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("README.md")}
2025-06-03T23:30:17.002905Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:30:28.796171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("README.md")}
2025-06-03T23:30:28.796332Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:30:41.396639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("LOCAL_DEVELOPMENT_GUIDE.md")}
2025-06-03T23:30:41.396660Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:30:55.096376Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("TRANSITION_COMPLETE_SUMMARY.md")}
2025-06-03T23:30:55.096402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-03T23:36:49.314105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T23:36:49.315502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:37:51.706839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/LICENSE-SELECT2.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/inlines.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/RelatedObjectLookups.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/widgets.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/jquery.init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/forms.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectFilter2.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dark_mode.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/core.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/collapse.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive_rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/DateTimeShortcuts.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dashboard.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/cancel.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate_init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/urlify.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/changelists.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/login.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/nav_sidebar.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/calendar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/LICENSE.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/actions.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectBox.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/popup_response.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/theme.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/change_form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/nav_sidebar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/filters.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2")}
2025-06-03T23:37:51.707635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:37:56.998646Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/inline-delete.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/default.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/76a0395d556289dc.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.autocomplete.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/km.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hsb.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bn.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-changelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-alert.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/webpack.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/gl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/bootstrap.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/prettify.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.eot"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-addlink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tk.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hy.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-viewlink.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hu.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/favicon.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/README.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ar.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/jquery-3.5.1.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hi.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/backend/logs/django.log"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/coreapi-0.1.1.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/default.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ms.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.ajaxQueue.js"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/font-awesome-4.0.3.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_on.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ka.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sv.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages/_error.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/favicon.ico"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css.map"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/base.css"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/react-refresh.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ce619c7b82688e90.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-calendar.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-TW.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.ttf"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/dsb.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-arrowright.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ps.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/ajax-form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ja.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css.map"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.eot"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/de.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/calendar-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ca.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/jquery.json-view.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/grid.png"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-no.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lv.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/csrf.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis"), AnchoredSystemPathBuf("apps/frontend/.next/fallback-build-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sk.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/da.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fa.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ro.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ce619c7b82688e90.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_off.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/api.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sq.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/prettify-min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ko.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nb.js"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/et.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt-BR.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/highlight.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/jquery.json-view.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.woff"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/az.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.bgiframe.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/highlight.pack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-CN.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/_error.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lt.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.76a0395d556289dc.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/LICENSE"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings.png"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/is.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/css/jquery.autocomplete.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/el.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_document.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/sorting-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr-Cyrl.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/mk.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/es.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/main.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bg.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages/_app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/it.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/cs.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/id.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js.map"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ne.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/amp.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js.map"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-add.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings-white.png"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages/_app.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-clock.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown-alt.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/search.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/selector-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/he.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/grid.png"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/vi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/fallback/pages"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/pages/_error.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_error.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/en.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/img/indicator.gif"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages/_app.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/af.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ru.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-deletelink.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bs.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/img"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-tweaks.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/th.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.ttf"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/eu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/uk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-no.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/react-refresh.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tr.js")}
2025-06-03T23:37:56.999205Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }, WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:37:56.999414Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:37:57.003794Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:37:57.598987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_")}
2025-06-03T23:37:57.599007Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:37:57.599275Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:37:57.698947Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz")}
2025-06-03T23:37:57.698961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:37:57.699083Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:37:58.798666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>")}
2025-06-03T23:37:58.798684Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:37:58.798812Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:37:59.900850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2")}
2025-06-03T23:37:59.900896Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:37:59.901020Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:00.198572Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json")}
2025-06-03T23:38:00.198582Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:00.198660Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:00.398179Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/a87f2f1b708c0748.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.a87f2f1b708c0748.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.a87f2f1b708c0748.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js")}
2025-06-03T23:38:00.398187Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:00.398231Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:00.598623Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz")}
2025-06-03T23:38:00.598630Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:00.598669Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:01.499668Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T23:38:01.499677Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:01.499780Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:01.799007Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_")}
2025-06-03T23:38:01.799015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:01.799069Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:58.800806Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.59bcab399dc10afb.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/59bcab399dc10afb.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js")}
2025-06-03T23:38:58.800864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:58.801043Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:38:58.898764Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js")}
2025-06-03T23:38:58.898772Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:38:58.898817Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:39:38.199195Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>")}
2025-06-03T23:39:38.199221Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:39:38.227865Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:39:38.400668Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1cffd34ab6b5aaf8.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1cffd34ab6b5aaf8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-03T23:39:38.400678Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:39:38.400738Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:40:38.401783Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_")}
2025-06-03T23:40:38.401807Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:40:38.416698Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:40:38.601660Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T23:40:38.601668Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:40:38.601770Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:40:38.800758Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T23:40:38.800765Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:40:38.808158Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:40:38.901136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_")}
2025-06-03T23:40:38.901144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:40:38.901182Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:40:39.101206Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz")}
2025-06-03T23:40:39.101213Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:40:39.101253Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:41:20.807149Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>")}
2025-06-03T23:41:20.807629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:41:20.820770Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:41:21.104390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ff0c5a40503a7d07.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ff0c5a40503a7d07.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-03T23:41:21.104399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:41:21.104454Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:42:21.114317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_")}
2025-06-03T23:42:21.115119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:42:21.154402Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:42:21.205315Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_")}
2025-06-03T23:42:21.205346Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:42:21.205774Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:42:21.405363Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_")}
2025-06-03T23:42:21.405372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:42:21.405423Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:42:21.504989Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_")}
2025-06-03T23:42:21.505142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:42:21.510329Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:42:21.603533Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz")}
2025-06-03T23:42:21.603542Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:42:21.616040Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:44.517709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/layout/unified-sidebar.tsx")}
2025-06-03T23:49:44.518716Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:46.311616Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/page.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/c9b5ca1ee1ae9552.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js")}
2025-06-03T23:49:46.311640Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:46.325487Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:46.608416Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json")}
2025-06-03T23:49:46.608427Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:46.621574Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:47.508902Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_")}
2025-06-03T23:49:47.508921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:47.509060Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:47.709041Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_")}
2025-06-03T23:49:47.709051Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:47.709128Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:48.507626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T23:49:48.507640Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:48.521040Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:48.608053Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T23:49:48.608062Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:48.608139Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:48.707994Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_")}
2025-06-03T23:49:48.708001Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:48.708057Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:49:48.808516Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz")}
2025-06-03T23:49:48.808524Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:49:48.808563Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:03.707987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/components/layout/unified-sidebar.tsx")}
2025-06-03T23:50:03.708012Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:03.907939Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json")}
2025-06-03T23:50:03.907948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:03.908007Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:04.107850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/d9d0486fcc1c7d95.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.d9d0486fcc1c7d95.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/page.d9d0486fcc1c7d95.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.d9d0486fcc1c7d95.hot-update.js")}
2025-06-03T23:50:04.107859Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:04.107911Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:21.508546Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>")}
2025-06-03T23:50:21.508579Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:21.508654Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:21.709075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/cf996159a8c6808b.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.cf996159a8c6808b.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js")}
2025-06-03T23:50:21.709085Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:21.709146Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:24.409881Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json")}
2025-06-03T23:50:24.410165Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:24.410581Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:24.508299Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/4bea1610e4ba0908.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.4bea1610e4ba0908.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js")}
2025-06-03T23:50:24.508309Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:24.508360Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:25.808473Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts")}
2025-06-03T23:50:25.808500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:25.821798Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:26.108131Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.a622ae44a67769b8.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/a622ae44a67769b8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js")}
2025-06-03T23:50:26.108141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:26.108323Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:37.709552Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/coreapi-0.1.1.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/nav_sidebar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/favicon.ico"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectBox.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fa.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/LICENSE.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ja.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/api.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/widgets.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/grid.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/it.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ca.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-TW.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-tweaks.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ro.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-add.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/cs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt-BR.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectFilter2.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_off.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/jquery-3.5.1.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sv.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/jquery.json-view.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/km.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/LICENSE-SELECT2.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate_init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ms.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/jquery.json-view.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/grid.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/prettify-min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hsb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sq.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-no.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings-white.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/default.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ka.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/et.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/theme.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/search.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ne.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/login.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/bootstrap.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/filters.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/de.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/ajax-form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/he.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bn.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-calendar.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hy.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/calendar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/popup_response.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/es.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dark_mode.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown-alt.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ko.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/LICENSE"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/el.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/da.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dashboard.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/selector-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/inlines.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/az.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/th.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-CN.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/collapse.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/is.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-arrowright.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/actions.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/id.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/highlight.pack.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/README.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/core.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/font-awesome-4.0.3.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/changelists.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/uk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/default.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/sorting-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lv.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/af.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/DateTimeShortcuts.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-viewlink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/gl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/change_form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ru.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/dsb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-addlink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-deletelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/urlify.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bg.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_on.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ps.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/nav_sidebar.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-alert.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/eu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/RelatedObjectLookups.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/jquery.init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/mk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-changelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/prettify.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/cancel.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/csrf.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/highlight.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/forms.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/en.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-clock.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive_rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/inline-delete.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr-Cyrl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/calendar-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/vi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/favicon.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js.map")}
2025-06-03T23:50:37.709589Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:50:42.261261Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/az.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/csrf.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt-BR.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/prettify-min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/change_form.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/cancel.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.ttf"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lv.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/popup_response.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_off.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bg.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ka.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/RelatedObjectLookups.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff"), AnchoredSystemPathBuf("apps/frontend/.next/_events.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/theme.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate_init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/prettify.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/inlines.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ca.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/nav_sidebar.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-changelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sq.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/LICENSE"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.eot"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/urlify.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-calendar.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown-alt.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/LICENSE.txt"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-viewlink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-add.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/vi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-arrowright.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/login.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-no.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/LICENSE-SELECT2.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/id.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ps.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr-Cyrl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/jquery.json-view.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/core.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/coreapi-0.1.1.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/en.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/bootstrap.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive_rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/he.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.ajaxQueue.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/mk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/cs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/actions.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/default.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectBox.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/et.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/km.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-addlink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/filters.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/it.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/es.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/highlight.pack.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ar.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-deletelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/calendar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/dsb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/el.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hy.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/is.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ms.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/da.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-alert.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dashboard.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-init.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/calendar-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ko.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/jquery-3.5.1.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/de.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/README.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/nav_sidebar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/ajax-form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/widgets.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hsb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-no.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.eot"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-TW.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/uk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/jquery.init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fa.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/img/indicator.gif"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-clock.svg"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/changelists.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/api.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_on.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/LICENSE.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sv.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/highlight.css"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/af.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ro.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/favicon.ico"), AnchoredSystemPathBuf("apps/backend/logs/django.log"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-tweaks.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/css/jquery.autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/grid.png"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/grid.png"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/search.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/inline-delete.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectFilter2.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/gl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/collapse.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dark_mode.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/forms.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bn.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/selector-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/DateTimeShortcuts.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/eu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nb.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ru.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/font-awesome-4.0.3.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/jquery.json-view.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ne.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings-white.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/th.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/sorting-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.woff"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/0.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/5.pack.gz"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ja.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.bgiframe.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/default.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/favicon.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-CN.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.ttf"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js")}
2025-06-03T23:50:42.263499Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:42.267764Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:42.271332Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:45.309199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js")}
2025-06-03T23:50:45.309234Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:45.345037Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:45.407967Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/37ea40b6c2271763.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.37ea40b6c2271763.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json")}
2025-06-03T23:50:45.407976Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:45.420825Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:50:59.609979Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/7bb0bc60aa84914e.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.7bb0bc60aa84914e.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js")}
2025-06-03T23:50:59.610096Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:50:59.636451Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:51:00.309768Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts")}
2025-06-03T23:51:00.309804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:51:00.323551Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:51:18.624709Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/trace")}
2025-06-03T23:51:18.625731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:51:18.725497Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:51:44.709816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/logs/django.log")}
2025-06-03T23:51:44.709852Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:51:54.309394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/login.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/change_form.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/widgets.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/forms.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/core.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/calendar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectFilter2.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/DateTimeShortcuts.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/LICENSE.txt"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive_rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/LICENSE-SELECT2.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/actions.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/popup_response.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/changelists.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/collapse.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/nav_sidebar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dashboard.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/jquery.init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/cancel.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/urlify.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/inlines.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/RelatedObjectLookups.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate_init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dark_mode.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/theme.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/nav_sidebar.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/filters.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectBox.js")}
2025-06-03T23:51:54.309419Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }}))
2025-06-03T23:51:59.076387Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/nav_sidebar.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/59bcab399dc10afb.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/gl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/urlify.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/cancel.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/favicon.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/favicon.ico"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/prettify-min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/responsive_rtl.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/jquery.json-view.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css.map"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/jquery-3.5.1.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-viewlink.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/cf996159a8c6808b.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/collapse.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.ttf"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.7bb0bc60aa84914e.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/README.txt"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dashboard.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/th.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-init.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/csrf.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/km.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ja.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ru.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js.map"), AnchoredSystemPathBuf("apps/frontend/.next/static/media"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/de.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/highlight.css"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1cffd34ab6b5aaf8.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/rtl.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/img/grid.png"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/LICENSE.txt"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/actions.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.ttf"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-no.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/coreapi-0.1.1.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hsb.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/is.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/cs.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-CN.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/RelatedObjectLookups.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings-white.png"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/LICENSE"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/bootstrap.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ca.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/es.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/redoc.standalone.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ro.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.autocomplete.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/api.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.a87f2f1b708c0748.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui.css.map"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1cffd34ab6b5aaf8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/theme.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/default.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.59bcab399dc10afb.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/zh-TW.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/LICENSE-SELECT2.md"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-add.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/af.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/id.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate_init.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/inlines.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/vendor/select2/select2.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/jquery.json-view.min.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectBox.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ka.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.bgiframe.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/forms.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/changelists.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-clock.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tr.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/it.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/LICENSE.md"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.cf996159a8c6808b.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/admin/DateTimeShortcuts.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-alert.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/css/base.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/el.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/grid.png"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/js/ajax-form.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/calendar-icons.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-no.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/d9d0486fcc1c7d95.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bn.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/popup_response.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/autocomplete.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-addlink.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/a87f2f1b708c0748.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/page.d9d0486fcc1c7d95.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/c9b5ca1ee1ae9552.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-deletelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/font-awesome-4.0.3.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard/page.c9b5ca1ee1ae9552.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.d9d0486fcc1c7d95.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fa.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ff0c5a40503a7d07.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/widgets.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lt.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/uk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/js/jquery.ajaxQueue.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/inline-delete.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/interception-route-rewrite-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/core.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/dsb.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/calendar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/nav_sidebar.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt-BR.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.a87f2f1b708c0748.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.a622ae44a67769b8.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/search.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/default.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sl.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/sorting-icons.svg"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ne.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/fi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/ninja/swagger-ui-bundle.js.map"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/bg.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/base.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-calendar.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/img/glyphicons-halflings.png"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ar.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/tooltag-arrowright.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-changelink.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/autocomplete.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/eu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/img/indicator.gif"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/da.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/mk.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.d9d0486fcc1c7d95.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sq.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/vi.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hi.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/docs/js/highlight.pack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.eot"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pt.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/polyfills.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/fontawesome-webfont.woff"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-tweaks.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/et.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/_not-found/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/prettify.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/7bb0bc60aa84914e.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/filters.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_on.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/jquery.init.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/login.css"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/nb.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/az.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/tk.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sv.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/css/dark_mode.css"), AnchoredSystemPathBuf("apps/backend/staticfiles/django_extensions/css/jquery.autocomplete.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/selector-icons.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/jquery/jquery.min.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/en.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/he.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.4bea1610e4ba0908.hot-update.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ms.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ps.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/lv.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/ko.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/select2.full.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap-theme.min.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/logs/django.log"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.eot"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/fonts/glyphicons-halflings-regular.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/guardian/img/icon-yes.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/SelectFilter2.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/rest_framework/css/bootstrap.min.css.map"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/4bea1610e4ba0908.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ff0c5a40503a7d07.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/trace"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/gis/move_vertex_off.svg"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/prepopulate.js"), AnchoredSystemPathBuf("apps/frontend/.next/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/img/icon-unknown-alt.svg"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/a622ae44a67769b8.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/LICENSE.txt"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.37ea40b6c2271763.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/sr-Cyrl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/37ea40b6c2271763.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/pl.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/xregexp/xregexp.min.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hy.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/vendor/select2/i18n/hu.js"), AnchoredSystemPathBuf("apps/backend/staticfiles/admin/js/change_form.js")}
2025-06-03T23:51:59.077015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/backend"), path: AnchoredSystemPathBuf("apps/backend") }, WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:51:59.082183Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:51:59.085610Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:51:59.409914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sync-external-store@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/zustand@4.5.7_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/sonner@1.7.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@tanstack+react-query-devtools@5.79.0_@tanstack+react-query@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next-themes@0.4.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>")}
2025-06-03T23:51:59.409970Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:51:59.442570Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:00.509568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_")}
2025-06-03T23:52:00.509600Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:00.524722Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:00.609343Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/media")}
2025-06-03T23:52:00.609354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:00.624064Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:00.710215Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/css/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/029616a09f89e9be-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/555550940b3f3995-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/443b65745b6df830-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/ceced752341815e9-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/c3d28d0a4af5320e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3612c969537d1f89-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/683edc0915df848c-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/0612184e5fd566f7-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3abc2a74bccacda0-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/3ad6e6f895797568-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/54685c0c990b5328-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/8cb74166f4c238e7-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b626faedfa289fd1-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/main-app.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/24803c66df11f210-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/static/css"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/dbdc77a4a9d637b5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/18d6d756b83deaa1-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/23acdaac58de6ef3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/5b5924403aa3f821-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/628cf4a163773be3-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/cc4bdf7bf08e5351-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d03708b9b9cff7ea-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9efac92d680e57b6-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/d3c939daec3cf0fb-s.p.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b45a9d9d1da72c1b-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/9003d31b0bf0a63e-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/css/app/layout.css"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/b53732993cf3ac57-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/e59a6a9b6eba13d5-s.woff2"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/media/078954fc9e8513ac-s.woff2")}
2025-06-03T23:52:00.710376Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:00.731076Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:00.809300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-3f626e.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/_app-pages-browser_node_modules_pnpm_tanstack_query-devtools_5_76_0_node_modules_tanstack_que-fef58b.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app-pages-internals.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/633457081244afec._.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js")}
2025-06-03T23:52:00.809311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:00.809369Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:01.109163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app/layout.e74ad18c95fa64d1.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/e74ad18c95fa64d1.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.e74ad18c95fa64d1.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/app"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/layout.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json")}
2025-06-03T23:52:01.109173Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:01.109228Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:01.210138Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_")}
2025-06-03T23:52:01.210148Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:01.210205Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:02.211030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_")}
2025-06-03T23:52:02.211058Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:02.242538Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:02.309146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_")}
2025-06-03T23:52:02.309155Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:02.309206Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:02.509405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/6.pack.gz_")}
2025-06-03T23:52:02.509412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:02.509498Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:49.209878Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@hookform+resolvers@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-hook-form@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-label@2.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-slot@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-primitive@2.0.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-compose-refs@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts")}
2025-06-03T23:52:49.209909Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:49.210491Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:52:49.310249Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.8a65756807143291.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/8a65756807143291.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/login"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js")}
2025-06-03T23:52:49.310259Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:52:49.310326Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:53:49.313074Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_")}
2025-06-03T23:53:49.313119Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:53:49.345969Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:53:49.411165Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_")}
2025-06-03T23:53:49.411182Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:53:49.433797Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:53:49.510036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_")}
2025-06-03T23:53:49.510050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:53:49.510104Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-03T23:53:49.610743Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/5.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/8.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/1.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz")}
2025-06-03T23:53:49.610752Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-03T23:53:49.610784Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun

2025-06-04T00:04:01.777872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-presence@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-controllable-state@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collapsible@1.1.2_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll@2.7.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-remove-scroll-bar@2.3.8_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-roving-focus@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-scope@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-size@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@floating-ui+react-dom@2.1.2_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-id@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-direction@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dialog@1.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-context@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-progress@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dismissable-layer@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/react-style-singleton@2.2.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-portal@1.1.3_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-arrow@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-layout-effect@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-collection@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-callback-ref@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-focus-guards@1.1.1_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/next@15.2.4_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-callback-ref@1.3.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-popper@1.2.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/use-sidecar@1.1.3_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-dropdown-menu@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>")}
2025-06-04T00:04:01.778913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:01.779615Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:01.875043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".turbo/daemon/f252bdf4524107f3-turbo.log.2025-06-04"), AnchoredSystemPathBuf(".turbo/daemon/f252bdf4524107f3-turbo.log.2025-06-03")}
2025-06-04T00:04:01.875053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-06-04T00:04:01.974828Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.db71b920621d6c90.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/db71b920621d6c90.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:01.974837Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:01.982033Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.273955Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-select@2.1.4_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-visually-hidden@1.1.1_@types+react-dom@19.1.5_@types+react@19.1.6_react-dom@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@radix-ui+react-use-previous@1.1.0_@types+react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js")}
2025-06-04T00:04:25.273989Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.274350Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.474008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2f4ebca846993dda.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2f4ebca846993dda.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:25.474021Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.499330Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:25.581993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/2f4ebca846993dda.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.2f4ebca846993dda.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js")}
2025-06-04T00:04:25.582004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:25.582238Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:26.774182Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js")}
2025-06-04T00:04:26.774204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:26.774528Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:26.876075Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.ed064bb1c8f0e5cf.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/ed064bb1c8f0e5cf.webpack.hot-update.json")}
2025-06-04T00:04:26.876089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:26.891406Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:28.673305Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts")}
2025-06-04T00:04:28.673317Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:28.673428Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:28.774163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/e96bd100076e181b.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.e96bd100076e181b.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/projects/new"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js")}
2025-06-04T00:04:28.774173Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:28.774238Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:30.273666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/package.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/clients/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app-paths-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page.js"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/layout.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/projects/new/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/vendor-chunks/lucide-react@<EMAIL>"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/founder-dashboard/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/server-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/pages-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/types/cache-life.d.ts"), AnchoredSystemPathBuf("apps/frontend/.next/types/app/login/page.ts"), AnchoredSystemPathBuf("apps/frontend/.next/server/webpack-runtime.js")}
2025-06-04T00:04:30.273687Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:30.274014Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:04:30.373765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/tasks/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/webpack.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-react-loadable-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/middleware-build-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/1c2711ca3b27846a.webpack.hot-update.json"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_ssgManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/development/_buildManifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/webpack/webpack.1c2711ca3b27846a.hot-update.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/clients/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/new/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/react-loadable-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/projects/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/server/next-font-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/founder-dashboard/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/app-build-manifest.json"), AnchoredSystemPathBuf("apps/frontend/.next/server/app/login/page_client-reference-manifest.js"), AnchoredSystemPathBuf("apps/frontend/.next/static/chunks/app/founder-dashboard/tasks/page.js")}
2025-06-04T00:04:30.373775Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:04:30.373910Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.491877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_")}
2025-06-04T00:05:30.492269Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.530788Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.674173Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_")}
2025-06-04T00:05:30.674190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.676826Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.773938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_")}
2025-06-04T00:05:30.773948Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.773991Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:30.974293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/6.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/2.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/server-development/index.pack.gz.old")}
2025-06-04T00:05:30.974301Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:30.974345Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-06-04T00:05:31.173861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/3.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/7.pack.gz"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/4.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz.old"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz_"), AnchoredSystemPathBuf("apps/frontend/.next/cache/webpack/client-development/index.pack.gz")}
2025-06-04T00:05:31.173869Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@mtbrmg/frontend"), path: AnchoredSystemPathBuf("apps/frontend") }}))
2025-06-04T00:05:31.173923Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun

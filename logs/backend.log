/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
Watching for file changes with StatReloader
Performing system checks...

System check identified some issues:

WARNINGS:
?: (guardian.W001) Guardian authentication backend is not hooked. You can add this in settings as eg: `AUTHENTICATION_BACKENDS = ('django.contrib.auth.backends.ModelBackend', 'guardian.backends.ObjectPermissionBackend')`.
?: (staticfiles.W004) The directory '/Users/<USER>/Sites/mtbrmg-erp-system/apps/backend/static' in the STATICFILES_DIRS setting does not exist.
?: (urls.W005) URL namespace 'authentication' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'clients' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'projects' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'tasks' isn't unique. You may not be able to reverse all URLs in this namespace
?: (urls.W005) URL namespace 'team' isn't unique. You may not be able to reverse all URLs in this namespace

System check identified 7 issues (0 silenced).
June 04, 2025 - 08:47:02
Django version 4.2.9, using settings 'mtbrmg_erp.settings'
Starting development server at http://127.0.0.1:8000/
Quit the server with CONTROL-C.

Unauthorized: /api/auth/profile/
"GET /api/auth/profile/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
Unauthorized: /api/
"GET /api/ HTTP/1.1" 401 62
"POST /api/auth/login/ HTTP/1.1" 200 950
Unauthorized: /api/projects/
Unauthorized: /api/tasks/
"GET /api/projects/ HTTP/1.1" 401 62
"GET /api/tasks/ HTTP/1.1" 401 62
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"OPTIONS /api/auth/login/ HTTP/1.1" 200 0
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"POST /api/auth/login/ HTTP/1.1" 200 950
"OPTIONS /api/finance/expenses/ HTTP/1.1" 200 0
Not Found: /api/finance/expenses/
"GET /api/finance/expenses/ HTTP/1.1" 404 32651

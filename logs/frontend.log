
> @mtbrmg/frontend@1.0.0 dev /Users/<USER>/Sites/mtbrmg-erp-system/apps/frontend
> next dev --port 3001

 ⨯ Failed to start server
Error: listen EADDRINUSE: address already in use :::3001
    at <unknown> (Error: listen EADDRINUSE: address already in use :::3001)
    at new Promise (<anonymous>) {
  code: 'EADDRINUSE',
  errno: -48,
  syscall: 'listen',
  address: '::',
  port: 3001
}
[?25h
                                                                                                                                 GET / 200 in 144ms
 ⚠ Cross origin request detected from mtbrmg.local to /_next/* resource. In a future major version of Next.js, you will need to explicitly configure "allowedDevOrigins" in next.config to allow this.
Read more: https://nextjs.org/docs/app/api-reference/config/next-config-js/allowedDevOrigins
 ✓ Compiled /login in 451ms (1104 modules)
 GET /login 200 in 535ms

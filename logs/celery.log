/Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/rest_framework_simplejwt/__init__.py:1: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import DistributionNotFound, get_distribution
 
 -------------- <EMAIL> v5.3.4 (emerald-rush)
--- ***** ----- 
-- ******* ---- macOS-15.5-arm64-arm-64bit 2025-06-04 08:29:07
- *** --- * --- 
- ** ---------- [config]
- ** ---------- .> app:         mtbrmg_erp:0x107a436e0
- ** ---------- .> transport:   redis://localhost:6379/0
- ** ---------- .> results:     redis://localhost:6379/0
- *** --- * --- .> concurrency: 8 (prefork)
-- ******* ---- .> task events: OFF (enable -E to monitor tasks in this worker)
--- ***** ----- 
 -------------- [queues]
                .> celery           exchange=celery(direct) key=celery
                

[tasks]
  . mtbrmg_erp.celery.debug_task

[2025-06-04 08:29:07,673: WARNING/MainProcess] /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-06-04 08:29:07,680: INFO/MainProcess] Connected to redis://localhost:6379/0
[2025-06-04 08:29:07,680: WARNING/MainProcess] /Users/<USER>/.pyenv/versions/3.12.2/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

[2025-06-04 08:29:07,681: INFO/MainProcess] mingle: searching for neighbors
[2025-06-04 08:29:08,688: INFO/MainProcess] mingle: all alone
[2025-06-04 08:29:08,714: INFO/MainProcess] <EMAIL> ready.

worker: Warm shutdown (MainProcess)
